import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'simple_root_screen.dart';
import 'services/supabase_auth_service.dart';
import 'services/account_service.dart';
import 'services/session_service.dart';
import 'services/multi_device_service.dart';
import 'services/supabase_sync_test.dart';
import 'services/user_presence_service.dart';
import 'forgot_password_screen.dart';
import 'device_limit_dialog.dart';

class SupabaseLoginScreen extends StatefulWidget {
  const SupabaseLoginScreen({super.key});

  @override
  State<SupabaseLoginScreen> createState() => _SupabaseLoginScreenState();
}

class _SupabaseLoginScreenState extends State<SupabaseLoginScreen> {
  final _authService = SupabaseAuthService();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isSignUp = false;
  bool _obscurePassword = true;
  String? _errorMessage;
  String _loadingMessage = '';

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _displayNameController.dispose();
    super.dispose();
  }

  // انتظار إنشاء الجلسة
  Future<bool> _waitForSession({int maxWaitSeconds = 10}) async {
    debugPrint('🔍 [SESSION] انتظار إنشاء الجلسة...');

    for (int i = 0; i < maxWaitSeconds * 2; i++) {
      final session = Supabase.instance.client.auth.currentSession;
      if (session != null) {
        debugPrint('✅ [SESSION] تم إنشاء الجلسة بنجاح بعد ${i * 0.5} ثانية');
        debugPrint(
          '✅ [SESSION] Access Token: ${session.accessToken.substring(0, 20)}...',
        );
        debugPrint('✅ [SESSION] انتهاء الجلسة: ${session.expiresAt}');
        return true;
      }

      debugPrint('⏳ [SESSION] انتظار... محاولة ${i + 1}');
      await Future.delayed(const Duration(milliseconds: 500));
    }

    debugPrint('❌ [SESSION] فشل في إنشاء الجلسة خلال $maxWaitSeconds ثانية');
    return false;
  }

  // الحصول على معرف الجهاز الثابت
  Future<String> getDeviceId() async {
    try {
      // أولاً، محاولة الحصول على المعرف المحفوظ محلياً
      final prefs = await SharedPreferences.getInstance();
      String? savedDeviceId = prefs.getString('permanent_device_id');

      if (savedDeviceId != null && savedDeviceId.isNotEmpty) {
        debugPrint('🔍 [DEVICE_ID] استخدام المعرف المحفوظ: $savedDeviceId');
        return savedDeviceId;
      }

      // إذا لم يوجد معرف محفوظ، إنشاء معرف جديد
      final deviceInfo = DeviceInfoPlugin();
      String deviceId = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;

        // استخدام معرف ثابت للأندرويد
        // الأولوية: fingerprint > id > model+brand
        if (androidInfo.fingerprint.isNotEmpty) {
          deviceId = androidInfo.fingerprint;
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Fingerprint: $deviceId');
        } else if (androidInfo.id.isNotEmpty) {
          deviceId = androidInfo.id;
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Build ID: $deviceId');
        } else {
          // استخدام مزيج من model و brand كبديل
          deviceId =
              '${androidInfo.brand}_${androidInfo.model}_${androidInfo.device}';
          debugPrint('🔍 [DEVICE_ID] إنشاء معرف من Model+Brand: $deviceId');
        }

        deviceId = 'android_$deviceId';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        final iosDeviceId = iosInfo.identifierForVendor ?? 'unknown_ios';
        deviceId = 'ios_$iosDeviceId';
        debugPrint('🔍 [DEVICE_ID] إنشاء معرف iOS: $deviceId');
      } else {
        deviceId = 'unknown_platform_${DateTime.now().millisecondsSinceEpoch}';
      }

      // حفظ المعرف الجديد محلياً
      await prefs.setString('permanent_device_id', deviceId);
      debugPrint('✅ [DEVICE_ID] تم حفظ المعرف الجديد: $deviceId');

      return deviceId;
    } catch (e) {
      debugPrint('❌ [DEVICE_ID] خطأ في الحصول على معرف الجهاز: $e');
      // في حالة الخطأ، إنشاء معرف مؤقت
      final fallbackId =
          'error_device_id_${DateTime.now().millisecondsSinceEpoch}';

      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('permanent_device_id', fallbackId);
      } catch (e2) {
        debugPrint('❌ [DEVICE_ID] خطأ في حفظ المعرف الاحتياطي: $e2');
      }

      return fallbackId;
    }
  }

  // مسح معرف الجهاز المحفوظ (للاختبار فقط)
  Future<void> clearDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('permanent_device_id');
      debugPrint('🗑️ [DEVICE_ID] تم مسح معرف الجهاز المحفوظ');
    } catch (e) {
      debugPrint('❌ [DEVICE_ID] خطأ في مسح معرف الجهاز: $e');
    }
  }

  Future<void> _handleAuth() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _loadingMessage = _isSignUp
          ? 'جاري إنشاء الحساب...'
          : 'جاري تسجيل الدخول...';
    });

    try {
      if (_isSignUp) {
        await _handleSignUp();
      } else {
        await _handleSignIn();
      }
    } catch (e) {
      debugPrint('❌ خطأ في المصادقة: $e');

      // معالجة خطأ الجهاز المرتبط بحساب آخر
      if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
        await _showDeviceLimitDialog();
      } else {
        setState(() {
          _errorMessage = _getErrorMessage(e.toString());
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
        _loadingMessage = '';
      });
    }
  }

  /// معالجة إنشاء حساب جديد
  Future<void> _handleSignUp() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final displayName = _displayNameController.text.trim();

    if (displayName.isEmpty) {
      throw Exception('اسم المستخدم مطلوب للتسجيل');
    }

    // فحص الجهاز قبل إنشاء الحساب
    setState(() => _loadingMessage = 'فحص الجهاز...');
    final deviceId = await getDeviceId();
    final existingUserId = await AccountService.getExistingAccountForDevice(
      deviceId,
    );

    if (existingUserId != null) {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              DeviceLimitDialog(existingUserId: existingUserId),
        );
      }
      return;
    }

    // إنشاء الحساب
    setState(() => _loadingMessage = 'إنشاء الحساب...');
    final response = await _authService.signUpWithEmail(
      email: email,
      password: password,
    );

    if (response.user != null) {
      setState(() => _loadingMessage = 'إعداد الحساب...');
      await _setupNewAccount(response.user!, displayName, deviceId);
    }
  }

  /// معالجة تسجيل الدخول
  Future<void> _handleSignIn() async {
    final email = _emailController.text.trim();
    final password = _passwordController.text;

    final response = await _authService.signInWithEmail(
      email: email,
      password: password,
    );

    if (response.user != null) {
      await _setupExistingAccount(response.user!);
    }
  }

  /// إعداد حساب جديد بعد التسجيل
  Future<void> _setupNewAccount(
    User user,
    String displayName,
    String deviceId,
  ) async {
    try {
      debugPrint('🔍 [SETUP] بدء إعداد الحساب للمستخدم: ${user.id}');

      // انتظار إنشاء الجلسة
      final sessionCreated = await _waitForSession(maxWaitSeconds: 10);
      if (!sessionCreated) {
        throw Exception('فشل في إنشاء الجلسة');
      }

      // اختبار المزامنة مع Supabase
      setState(() => _loadingMessage = 'اختبار المزامنة...');
      final syncTest = await SupabaseSyncTest.performFullSyncTest();
      if (!syncTest.overallSuccess) {
        debugPrint('⚠️ [SETUP] فشل اختبار المزامنة: ${syncTest.errorMessage}');
        // المتابعة مع تحذير
      }

      // تحديث display name
      setState(() => _loadingMessage = 'تحديث البيانات...');
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(data: {'display_name': displayName}),
      );

      // إنشاء سجل الحساب
      setState(() => _loadingMessage = 'إنشاء سجل الحساب...');
      try {
        await AccountService.createAccountV2(
          user.id,
          displayName,
          user.email ?? '',
        );
      } catch (e) {
        // ✅ فحص إذا كان الخطأ يتطلب إغلاق التطبيق
        if (e.toString().contains('FORCE_EXIT')) {
          debugPrint('🚪 [LOGIN] إغلاق التطبيق بسبب عدم وجود المستخدم');

          if (mounted) {
            setState(() => _loadingMessage = 'خطأ في التحقق من الحساب...');
            await Future.delayed(const Duration(seconds: 2));

            // إنهاء الجلسة والعودة لشاشة تسجيل الدخول
            await Supabase.instance.client.auth.signOut();
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/login');
            }
          }
          return;
        }
        rethrow; // إعادة رمي الخطأ إذا لم يكن FORCE_EXIT
      }

      // ربط الجهاز
      setState(() => _loadingMessage = 'ربط الجهاز...');
      await AccountService.linkDevice(
        user.id,
        deviceId,
        deviceName: 'جهاز ${Platform.isAndroid ? 'Android' : 'iOS'}',
      );

      // حفظ البيانات محلياً
      setState(() => _loadingMessage = 'حفظ البيانات...');
      await _saveUserData(user, displayName: displayName);

      // تهيئة خدمة تتبع الحالة
      setState(() => _loadingMessage = 'تهيئة خدمة الحضور...');
      await UserPresenceService.initialize();

      // الانتقال للشاشة الرئيسية
      setState(() => _loadingMessage = 'اكتمال الإعداد...');
      await _navigateToHome();
    } catch (e) {
      debugPrint('❌ [SETUP] خطأ في إعداد الحساب: $e');

      if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
        if (mounted) {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const DeviceLimitDialog(existingUserId: ''),
          );
        }
        return;
      }

      throw Exception('فشل في إعداد الحساب: ${e.toString()}');
    }
  }

  /// إعداد حساب موجود بعد تسجيل الدخول
  Future<void> _setupExistingAccount(User user) async {
    try {
      debugPrint('🔍 [SETUP] إعداد حساب موجود للمستخدم: ${user.id}');

      // انتظار إنشاء الجلسة
      final sessionCreated = await _waitForSession(maxWaitSeconds: 5);
      if (!sessionCreated) {
        throw Exception('فشل في إنشاء الجلسة للمستخدم الحالي');
      }

      // اختبار المزامنة مع Supabase
      setState(() => _loadingMessage = 'فحص المزامنة...');
      final syncTest = await SupabaseSyncTest.performFullSyncTest();
      if (!syncTest.overallSuccess) {
        debugPrint(
          '⚠️ [SETUP] تحذير: مشكلة في المزامنة: ${syncTest.errorMessage}',
        );
        // المتابعة مع البيانات المحلية إذا أمكن
      }

      // التحقق من وجود سجل الحساب وفقاً للنظام الجديد
      setState(() => _loadingMessage = 'جلب بيانات الحساب...');
      final existingAccount = await AccountService.getAccountDataV2(user.id);

      if (existingAccount == null) {
        // إنشاء سجل للمستخدم الحالي
        final userDisplayName =
            user.userMetadata?['display_name'] ??
            user.email?.split('@')[0] ??
            'مستخدم';

        await AccountService.createAccountV2(
          user.id,
          userDisplayName,
          user.email ?? '',
        );
        debugPrint('✅ [SETUP] تم إنشاء سجل user_accounts للمستخدم الحالي');
      } else {
        // تسجيل حالة الحساب (بدون منع تسجيل الدخول)
        final accountStatus = existingAccount['account_status'] as String;
        debugPrint('✅ [SETUP] حالة الحساب الموجود: $accountStatus');

        // حفظ حالة الحساب في SharedPreferences للاستخدام داخل التطبيق
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('account_status', accountStatus);
        await prefs.setBool('is_locked', existingAccount['is_locked'] == true);
        if (existingAccount['lock_reason'] != null) {
          await prefs.setString('lock_reason', existingAccount['lock_reason']);
        }

        // تسجيل معلومات إضافية للمراجعة
        if (accountStatus == 'expired') {
          debugPrint(
            '⚠️ [SETUP] الحساب منتهي - سيتم تطبيق القيود داخل التطبيق',
          );
        } else if (accountStatus == 'banned') {
          debugPrint(
            '⚠️ [SETUP] الحساب محظور - سيتم تطبيق القيود داخل التطبيق',
          );
        } else if (accountStatus == 'suspended') {
          debugPrint(
            '⚠️ [SETUP] الحساب موقوف - سيتم تطبيق القيود داخل التطبيق',
          );
        } else if (existingAccount['is_locked'] == true) {
          debugPrint('⚠️ [SETUP] الحساب مقفل - سيتم تطبيق القيود داخل التطبيق');
        }
      }

      // ربط الجهاز وفقاً للنظام الجديد
      setState(() => _loadingMessage = 'ربط الجهاز...');
      final deviceId = await getDeviceId();
      final deviceLinked = await MultiDeviceService.linkDeviceV2(
        user.id,
        deviceId,
        deviceName: 'جهاز ${Platform.isAndroid ? 'Android' : 'iOS'}',
        deviceType: Platform.isAndroid ? 'android' : 'ios',
      );

      if (!deviceLinked) {
        throw Exception('فشل في ربط الجهاز بالحساب');
      }

      // تهيئة جلسة نشطة
      setState(() => _loadingMessage = 'تهيئة الجلسة...');
      final sessionInitialized = await SessionService.initialize();
      if (!sessionInitialized) {
        debugPrint('⚠️ [SETUP] تحذير: فشل في تهيئة الجلسة');
      }

      // حفظ البيانات محلياً
      await _saveUserData(user);

      // تهيئة خدمة تتبع الحالة
      await UserPresenceService.initialize();

      // الانتقال للشاشة الرئيسية
      await _navigateToHome();
    } catch (e) {
      debugPrint('❌ [SETUP] خطأ في إعداد الحساب الموجود: $e');

      if (e.toString().contains('انتهت الفترة التجريبية')) {
        rethrow;
      }

      if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
        rethrow;
      }

      throw Exception('فشل في إعداد بيانات المستخدم: ${e.toString()}');
    }
  }

  /// عرض حوار تحذير الجهاز المرتبط بحساب آخر
  Future<void> _showDeviceLimitDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تحذير'),
          content: const Text(
            'يوجد حساب آخر مرتبط بهذا الجهاز.\n'
            'لا يمكن إنشاء أكثر من حساب واحد في نفس الجهاز.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('موافق'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  String _getErrorMessage(String error) {
    if (error.contains('Invalid login credentials')) {
      return 'بيانات تسجيل الدخول غير صحيحة';
    } else if (error.contains('User already registered')) {
      return 'هذا البريد مسجل مسبقاً، جرب تسجيل الدخول';
    } else if (error.contains('Invalid email')) {
      return 'البريد الإلكتروني غير صحيح';
    } else if (error.contains('Password should be at least 6 characters')) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else if (error.contains('انتهت الفترة التجريبية')) {
      return 'انتهت الفترة التجريبية. يرجى تفعيل الحساب للمتابعة.';
    } else if (error.contains('فشل في إنشاء الجلسة')) {
      return 'مشكلة في الاتصال. يرجى المحاولة مرة أخرى.';
    } else if (error.contains('هذا الحساب مرتبط') ||
        error.contains('يوجد حساب آخر')) {
      return error;
    }
    return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  }

  Future<void> _saveUserData(User user, {String? displayName}) async {
    debugPrint('💾 [LOGIN] بدء حفظ بيانات المستخدم...');
    final prefs = await SharedPreferences.getInstance();

    // حفظ بيانات المستخدم
    await prefs.setString('user_id', user.id);
    await prefs.setString('user_email', user.email ?? '');
    debugPrint('✅ [LOGIN] تم حفظ معرف المستخدم والإيميل');

    // حفظ اسم المستخدم من البيانات أو المعامل
    final userName =
        displayName ??
        user.userMetadata?['display_name'] ??
        user.email?.split('@')[0] ??
        'مستخدم';
    await prefs.setString('user_display_name', userName);
    debugPrint('✅ [LOGIN] تم حفظ اسم المستخدم: $userName');

    await prefs.setBool('is_logged_in', true);
    debugPrint('✅ [LOGIN] تم تعيين is_logged_in = true');

    // إعداد بيانات التجربة (30 يوم)
    final now = DateTime.now();
    final expiryDate = now.add(const Duration(days: 30));
    await prefs.setInt('expiry_millis', expiryDate.millisecondsSinceEpoch);
    await prefs.setBool('is_trial', true);
    debugPrint('✅ [LOGIN] تم إعداد بيانات التجربة');

    // حفظ معلومات الجهاز
    await _saveDeviceInfo(user.id);

    // تأكيد حفظ البيانات
    final savedLogin = prefs.getBool('is_logged_in');
    final savedUserId = prefs.getString('user_id');
    debugPrint('🔍 [LOGIN] تأكيد الحفظ:');
    debugPrint('  is_logged_in: $savedLogin');
    debugPrint('  user_id: $savedUserId');
    debugPrint('💾 [LOGIN] انتهى حفظ بيانات المستخدم بنجاح');
  }

  Future<void> _saveDeviceInfo(String userId) async {
    final deviceId = await getDeviceId();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('device_id', deviceId);
  }

  Future<void> _navigateToHome() async {
    if (!mounted) return;
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (_) => SimpleRootScreen(
          onLoginSuccess: () {
            // سيتم تنفيذ المزامنة بعد تسجيل الدخول
          },
        ),
      ),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.primary,
              colorScheme.primary.withValues(alpha: 0.8),
              colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),
                // شعار التطبيق
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.cell_tower_rounded,
                    size: 60,
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'iTower',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'لوحة تحكم احترافية لإدارة المشتركين والديون',
                  style: TextStyle(
                    fontSize: 17,
                    color: colorScheme.onPrimary.withValues(alpha: 0.92),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                // نموذج تسجيل الدخول
                Card(
                  elevation: 0,
                  color: colorScheme.surface.withValues(
                    alpha: isDark ? 0.7 : 0.93,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          // تبديل بين تسجيل الدخول والتسجيل
                          Row(
                            children: [
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      setState(() => _isSignUp = false),
                                  style: TextButton.styleFrom(
                                    backgroundColor: !_isSignUp
                                        ? colorScheme.primary
                                        : Colors.transparent,
                                    foregroundColor: !_isSignUp
                                        ? colorScheme.onPrimary
                                        : colorScheme.onSurface,
                                  ),
                                  child: const Text('تسجيل الدخول'),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      setState(() => _isSignUp = true),
                                  style: TextButton.styleFrom(
                                    backgroundColor: _isSignUp
                                        ? colorScheme.primary
                                        : Colors.transparent,
                                    foregroundColor: _isSignUp
                                        ? colorScheme.onPrimary
                                        : colorScheme.onSurface,
                                  ),
                                  child: const Text('إنشاء حساب'),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          // حقل البريد الإلكتروني
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: const InputDecoration(
                              labelText: 'البريد الإلكتروني',
                              prefixIcon: Icon(Icons.email),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال البريد الإلكتروني';
                              }
                              if (!value.contains('@')) {
                                return 'البريد الإلكتروني غير صحيح';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // حقل اسم المستخدم (للتسجيل فقط)
                          if (_isSignUp) ...[
                            TextFormField(
                              controller: _displayNameController,
                              decoration: const InputDecoration(
                                labelText: 'اسم المستخدم',
                                prefixIcon: Icon(Icons.person),
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (_isSignUp &&
                                    (value == null || value.trim().isEmpty)) {
                                  return 'اسم المستخدم مطلوب';
                                }
                                if (value != null && value.trim().length < 2) {
                                  return 'اسم المستخدم يجب أن يكون حرفين على الأقل';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                          ],

                          // حقل كلمة المرور
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            decoration: InputDecoration(
                              labelText: 'كلمة المرور',
                              prefixIcon: const Icon(Icons.lock),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility
                                      : Icons.visibility_off,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال كلمة المرور';
                              }
                              if (value.length < 6) {
                                return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 24),
                          // رسالة الخطأ
                          if (_errorMessage != null)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: colorScheme.error.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: colorScheme.error.withValues(
                                    alpha: 0.3,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    color: colorScheme.error,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      _errorMessage!,
                                      style: TextStyle(
                                        color: colorScheme.error,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          if (_errorMessage != null) const SizedBox(height: 16),
                          // أزرار العمل
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _handleAuth,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: _isLoading
                                  ? Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  Colors.white,
                                                ),
                                          ),
                                        ),
                                        if (_loadingMessage.isNotEmpty) ...[
                                          const SizedBox(height: 8),
                                          Text(
                                            _loadingMessage,
                                            style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ],
                                    )
                                  : Text(
                                      _isSignUp ? 'إنشاء حساب' : 'تسجيل الدخول',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                            ),
                          ),

                          // زر نسيت كلمة المرور (فقط في وضع تسجيل الدخول)
                          if (!_isSignUp) ...[
                            const SizedBox(height: 16),
                            TextButton(
                              onPressed: _isLoading
                                  ? null
                                  : () {
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const ForgotPasswordScreen(),
                                        ),
                                      );
                                    },
                              child: Text(
                                'نسيت كلمة المرور؟',
                                style: TextStyle(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                // معلومات إضافية
                Text(
                  'بتسجيل الدخول، أنت توافق على شروط الاستخدام',
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onPrimary.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
