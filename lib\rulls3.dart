-- ===================================================================
-- تحديث القواعد الأمنية لنظام التحديثات في Supabase
-- يجب تنفيذ هذا الملف في Supabase Dashboard > SQL Editor
-- ===================================================================

-- 1. التأكد من وجود دالة exec_sql (مطلوبة لتنفيذ SQL من التطبيق)
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS TEXT AS $$
BEGIN
    EXECUTE sql;
    RETURN 'تم تنفيذ الأمر بنجاح';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'خطأ: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح صلاحية تنفيذ الدالة للمستخدمين المصادق عليهم
GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;

-- 2. دالة للتحقق من صلاحيات الإدارة
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_accounts 
        WHERE user_id = auth.uid() 
        AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. تفعيل RLS على جداول التحديثات (إذا لم تكن مفعلة)
ALTER TABLE IF EXISTS app_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_update_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_update_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS update_statistics ENABLE ROW LEVEL SECURITY;

-- 4. حذف السياسات القديمة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can view active published updates" ON app_updates;
DROP POLICY IF EXISTS "Admins can manage all updates" ON app_updates;
DROP POLICY IF EXISTS "Service role can manage updates" ON app_updates;
DROP POLICY IF EXISTS "Admins can view update statistics" ON update_statistics;
DROP POLICY IF EXISTS "Service role can manage statistics" ON update_statistics;

-- 5. إنشاء السياسات الجديدة لجدول app_updates

-- المستخدمون العاديون يمكنهم قراءة التحديثات النشطة والمنشورة فقط
CREATE POLICY "Users can view active published updates" ON app_updates
    FOR SELECT USING (
        is_active = true 
        AND (published_at IS NULL OR published_at <= NOW())
    );

-- المدراء يمكنهم إدارة جميع التحديثات
CREATE POLICY "Admins can manage all updates" ON app_updates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_accounts 
            WHERE user_id = auth.uid() 
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- تطبيق الإدارة يمكنه إدارة جميع التحديثات (عبر service role)
CREATE POLICY "Service role can manage updates" ON app_updates
    FOR ALL USING (auth.role() = 'service_role');

-- 6. إنشاء السياسات لجدول إحصائيات التحديثات

-- المدراء فقط يمكنهم رؤية الإحصائيات
CREATE POLICY "Admins can view update statistics" ON update_statistics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_accounts 
            WHERE user_id = auth.uid() 
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- تطبيق الإدارة يمكنه إدارة الإحصائيات (عبر service role)
CREATE POLICY "Service role can manage statistics" ON update_statistics
    FOR ALL USING (auth.role() = 'service_role');

-- 7. منح الصلاحيات المطلوبة

-- منح صلاحيات للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON app_updates TO authenticated;
GRANT ALL ON user_update_history TO authenticated;
GRANT ALL ON user_update_preferences TO authenticated;
GRANT SELECT ON update_statistics TO authenticated;

-- منح صلاحيات لـ service role (تطبيق الإدارة)
GRANT ALL ON app_updates TO service_role;
GRANT ALL ON user_update_history TO service_role;
GRANT ALL ON user_update_preferences TO service_role;
GRANT ALL ON update_statistics TO service_role;

-- منح صلاحيات على التسلسلات
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- 8. إنشاء فهارس إضافية لتحسين الأداء (إذا لم تكن موجودة)
CREATE INDEX IF NOT EXISTS idx_app_updates_platform_active ON app_updates(platform, is_active);
CREATE INDEX IF NOT EXISTS idx_app_updates_build_number ON app_updates(build_number);
CREATE INDEX IF NOT EXISTS idx_app_updates_published_at ON app_updates(published_at);

-- 9. تحديث الجداول الموجودة لتتوافق مع النظام الجديد

-- إضافة عمود account_status إلى user_accounts إذا لم يكن موجود
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'account_status'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN account_status VARCHAR(20) DEFAULT 'trial';
        
        -- تحديث البيانات الموجودة
        UPDATE user_accounts SET account_status = 
            CASE 
                WHEN is_trial = true AND expiry_millis > EXTRACT(EPOCH FROM NOW()) * 1000 THEN 'trial'
                WHEN is_trial = false THEN 'active'
                ELSE 'expired'
            END;
    END IF;
END $$;

-- إضافة عمود trial_days_remaining إذا لم يكن موجود
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'trial_days_remaining'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN trial_days_remaining INTEGER DEFAULT 0;
        
        -- حساب الأيام المتبقية للحسابات التجريبية
        UPDATE user_accounts SET trial_days_remaining = 
            CASE 
                WHEN is_trial = true AND expiry_millis > EXTRACT(EPOCH FROM NOW()) * 1000 THEN 
                    GREATEST(0, FLOOR((expiry_millis - EXTRACT(EPOCH FROM NOW()) * 1000) / (24 * 60 * 60 * 1000)))
                ELSE 0
            END;
    END IF;
END $$;

-- إضافة عمود subscription_end إذا لم يكن موجود
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'subscription_end'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN subscription_end TIMESTAMP WITH TIME ZONE;
        
        -- تحديث تاريخ انتهاء الاشتراك للحسابات غير التجريبية
        UPDATE user_accounts SET subscription_end = 
            CASE 
                WHEN is_trial = false AND expiry_millis > 0 THEN 
                    TO_TIMESTAMP(expiry_millis / 1000)
                ELSE NULL
            END;
    END IF;
END $$;

-- 10. إنشاء دالة لتحديث حالة الحسابات تلقائياً
CREATE OR REPLACE FUNCTION update_account_status()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث حالة الحساب بناءً على البيانات الجديدة
    IF NEW.is_trial = true THEN
        IF NEW.expiry_millis > EXTRACT(EPOCH FROM NOW()) * 1000 THEN
            NEW.account_status = 'trial';
            NEW.trial_days_remaining = GREATEST(0, FLOOR((NEW.expiry_millis - EXTRACT(EPOCH FROM NOW()) * 1000) / (24 * 60 * 60 * 1000)));
        ELSE
            NEW.account_status = 'expired';
            NEW.trial_days_remaining = 0;
        END IF;
    ELSE
        NEW.account_status = 'active';
        NEW.trial_days_remaining = 0;
        IF NEW.expiry_millis > 0 THEN
            NEW.subscription_end = TO_TIMESTAMP(NEW.expiry_millis / 1000);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق الدالة على جدول user_accounts
DROP TRIGGER IF EXISTS update_account_status_trigger ON user_accounts;
CREATE TRIGGER update_account_status_trigger
    BEFORE INSERT OR UPDATE ON user_accounts
    FOR EACH ROW EXECUTE FUNCTION update_account_status();

-- 11. رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE 'تم تطبيق جميع القواعد الأمنية لنظام التحديثات بنجاح!';
    RAISE NOTICE 'الجداول المحدثة: app_updates, user_update_history, user_update_preferences, update_statistics';
    RAISE NOTICE 'تم تحديث جدول user_accounts ليتوافق مع النظام الجديد';
END $$;
