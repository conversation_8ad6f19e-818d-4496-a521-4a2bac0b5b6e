-- ===================================================================
-- نظام إدارة المستخدمين الاحترافي - Supabase Schema
-- تاريخ الإنشاء: 2025-01-27
-- الوصف: نظام شامل لإدارة المستخدمين مع أمان عالي وإحصائيات دقيقة
-- ===================================================================

-- تنظيف الجداول القديمة (احتياطي)
DROP TABLE IF EXISTS activity_log CASCADE;
DROP TABLE IF EXISTS daily_stats CASCADE;
DROP TABLE IF EXISTS active_sessions CASCADE;
DROP TABLE IF EXISTS user_devices CASCADE;
DROP TABLE IF EXISTS activation_codes CASCADE;
DROP TABLE IF EXISTS user_accounts CASCADE;

-- ===================================================================
-- 1. جدول المستخدمين الرئيسي (محسن)
-- ===================================================================
CREATE TABLE user_accounts (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  
  -- معلومات أساسية
  display_name TEXT NOT NULL DEFAULT '',
  email TEXT,
  phone TEXT,
  
  -- حالة الحساب
  account_status TEXT CHECK (account_status IN (
    'active',      -- نشط ومفعل
    'trial',       -- فترة تجريبية
    'suspended',   -- موقوف مؤقتاً
    'expired',     -- منتهي الصلاحية
    'banned'       -- محظور نهائياً
  )) DEFAULT 'trial',
  
  -- معلومات الاشتراك
  subscription_type TEXT CHECK (subscription_type IN (
    'trial',       -- تجريبي
    'monthly',     -- شهري
    'quarterly',   -- ربع سنوي
    'semi_annual', -- نصف سنوي
    'annual'       -- سنوي
  )) DEFAULT 'trial',
  
  subscription_start TIMESTAMPTZ,
  subscription_end TIMESTAMPTZ,
  trial_days_remaining INTEGER DEFAULT 15,

  -- ✅ إضافة تاريخ انتهاء الفترة التجريبية الواضح
  trial_start_date TIMESTAMPTZ DEFAULT NOW(),
  trial_end_date TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '15 days'),
  
  -- معلومات الأمان
  max_devices INTEGER DEFAULT 3,
  login_attempts INTEGER DEFAULT 0,
  last_login_attempt TIMESTAMPTZ,
  is_locked BOOLEAN DEFAULT false,
  lock_reason TEXT,
  
  -- تواريخ مهمة
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  
  -- معلومات إضافية
  notes TEXT,
  created_by UUID REFERENCES auth.users(id)
);

-- ===================================================================
-- 2. جدول الأجهزة المعتمدة
-- ===================================================================
CREATE TABLE user_devices (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- معلومات الجهاز
  device_id TEXT NOT NULL,
  device_name TEXT NOT NULL DEFAULT '',
  device_type TEXT CHECK (device_type IN ('android', 'ios', 'web')) NOT NULL,
  device_fingerprint TEXT,
  
  -- حالة الجهاز
  device_status TEXT CHECK (device_status IN (
    'active',      -- نشط
    'inactive',    -- غير نشط
    'blocked'      -- محظور
  )) DEFAULT 'active',
  
  -- معلومات الاتصال
  first_login TIMESTAMPTZ DEFAULT NOW(),
  last_used TIMESTAMPTZ DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  location_info JSONB,
  
  -- إحصائيات
  total_sessions INTEGER DEFAULT 0,
  total_usage_time INTERVAL DEFAULT '0 seconds',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, device_id)
);

-- ===================================================================
-- 3. جدول الجلسات النشطة
-- ===================================================================
CREATE TABLE active_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  
  -- معلومات الجلسة
  session_token TEXT UNIQUE NOT NULL,
  session_start TIMESTAMPTZ DEFAULT NOW(),
  last_heartbeat TIMESTAMPTZ DEFAULT NOW(),
  expected_end TIMESTAMPTZ,
  
  -- معلومات الاتصال
  ip_address INET,
  user_agent TEXT,
  app_version TEXT,
  
  -- حالة الجلسة
  is_active BOOLEAN DEFAULT true,
  
  -- ربط مع الأجهزة المعتمدة
  FOREIGN KEY (user_id, device_id) 
  REFERENCES user_devices(user_id, device_id) ON DELETE CASCADE
);

-- ===================================================================
-- 4. جدول أكواد التفعيل (محسن)
-- ===================================================================
CREATE TABLE activation_codes (
  id SERIAL PRIMARY KEY,
  
  -- معلومات الكود
  code TEXT UNIQUE NOT NULL,
  code_type TEXT CHECK (code_type IN (
    'trial_extension', -- تمديد تجريبي
    'monthly',         -- شهري
    'quarterly',       -- ربع سنوي
    'semi_annual',     -- نصف سنوي
    'annual'           -- سنوي
  )) NOT NULL,
  
  duration_days INTEGER NOT NULL,
  max_devices INTEGER DEFAULT 3,
  
  -- حالة الكود
  code_status TEXT CHECK (code_status IN (
    'active',    -- متاح للاستخدام
    'used',      -- مستخدم
    'expired',   -- منتهي الصلاحية
    'disabled'   -- معطل
  )) DEFAULT 'active',
  
  -- معلومات الاستخدام
  used_at TIMESTAMPTZ,
  used_by UUID REFERENCES auth.users(id),
  used_device_info JSONB,
  
  -- معلومات الإنشاء
  created_by UUID REFERENCES auth.users(id),
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- ملاحظات
  description TEXT,
  batch_id TEXT
);

-- ===================================================================
-- 5. جدول سجل الأنشطة (Audit Log)
-- ===================================================================
CREATE TABLE activity_log (
  id SERIAL PRIMARY KEY,
  
  -- معلومات الحدث
  user_id UUID REFERENCES auth.users(id),
  action_type TEXT NOT NULL,
  action_details JSONB,
  
  -- معلومات السياق
  device_id TEXT,
  ip_address INET,
  user_agent TEXT,
  
  -- معلومات المدير
  admin_user_id UUID REFERENCES auth.users(id),
  admin_reason TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================================================
-- 6. جدول الإحصائيات اليومية
-- ===================================================================
CREATE TABLE daily_stats (
  id SERIAL PRIMARY KEY,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  
  -- إحصائيات المستخدمين
  total_users INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  trial_users INTEGER DEFAULT 0,
  online_users INTEGER DEFAULT 0,
  new_users INTEGER DEFAULT 0,
  suspended_users INTEGER DEFAULT 0,
  
  -- إحصائيات الأكواد
  codes_used INTEGER DEFAULT 0,
  codes_created INTEGER DEFAULT 0,
  
  -- إحصائيات الجلسات
  total_sessions INTEGER DEFAULT 0,
  avg_session_duration INTERVAL,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(date)
);

-- ===================================================================
-- 7. الفهارس لتحسين الأداء
-- ===================================================================

-- فهارس user_accounts
CREATE INDEX idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX idx_user_accounts_status ON user_accounts(account_status);
CREATE INDEX idx_user_accounts_subscription ON user_accounts(subscription_type);
CREATE INDEX idx_user_accounts_expiry ON user_accounts(subscription_end);
CREATE INDEX idx_user_accounts_email ON user_accounts(email);

-- ✅ فهارس للأعمدة الجديدة
CREATE INDEX idx_user_accounts_trial_start ON user_accounts(trial_start_date);
CREATE INDEX idx_user_accounts_trial_end ON user_accounts(trial_end_date);
CREATE INDEX idx_user_accounts_trial_status ON user_accounts(account_status, trial_end_date) WHERE account_status = 'trial';

-- فهارس user_devices
CREATE INDEX idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX idx_user_devices_device_id ON user_devices(device_id);
CREATE INDEX idx_user_devices_status ON user_devices(device_status);
CREATE INDEX idx_user_devices_last_used ON user_devices(last_used);

-- فهارس active_sessions
CREATE INDEX idx_active_sessions_user_id ON active_sessions(user_id);
CREATE INDEX idx_active_sessions_device_id ON active_sessions(device_id);
CREATE INDEX idx_active_sessions_active ON active_sessions(is_active);
CREATE INDEX idx_active_sessions_heartbeat ON active_sessions(last_heartbeat);

-- فهارس activation_codes
CREATE INDEX idx_activation_codes_code ON activation_codes(code);
CREATE INDEX idx_activation_codes_status ON activation_codes(code_status);
CREATE INDEX idx_activation_codes_type ON activation_codes(code_type);
CREATE INDEX idx_activation_codes_used_by ON activation_codes(used_by);
CREATE INDEX idx_activation_codes_batch ON activation_codes(batch_id);

-- فهارس activity_log
CREATE INDEX idx_activity_log_user_id ON activity_log(user_id);
CREATE INDEX idx_activity_log_action_type ON activity_log(action_type);
CREATE INDEX idx_activity_log_created_at ON activity_log(created_at);
CREATE INDEX idx_activity_log_admin_user_id ON activity_log(admin_user_id);

-- فهارس daily_stats
CREATE INDEX idx_daily_stats_date ON daily_stats(date);

-- ===================================================================
-- 8. الدوال والـ Triggers
-- ===================================================================

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ✅ دالة تحديث حالة المستخدم المحسنة (مع منطق تاريخ انتهاء صحيح)
CREATE OR REPLACE FUNCTION update_user_status()
RETURNS TRIGGER AS $$
BEGIN
  -- ✅ تحديث حالة المستخدم بناءً على تاريخ انتهاء الاشتراك المدفوع فقط
  IF NEW.subscription_end IS NOT NULL AND NEW.subscription_end < NOW() THEN
    NEW.account_status = 'expired';
  -- ✅ تحديث حالة الفترة التجريبية بناءً على التاريخ الفعلي وليس العدد
  ELSIF NEW.account_status = 'trial' AND NEW.trial_end_date IS NOT NULL AND NEW.trial_end_date < NOW() THEN
    NEW.account_status = 'expired';
    NEW.trial_days_remaining = 0;
  -- ✅ حساب الأيام المتبقية للفترة التجريبية النشطة
  ELSIF NEW.account_status = 'trial' AND NEW.trial_end_date IS NOT NULL AND NEW.trial_end_date >= NOW() THEN
    NEW.trial_days_remaining = GREATEST(0, EXTRACT(DAY FROM (NEW.trial_end_date - NOW()))::INTEGER);
  -- ✅ إعداد تاريخ انتهاء افتراضي للحسابات الجديدة
  ELSIF NEW.account_status = 'trial' AND NEW.trial_end_date IS NULL THEN
    NEW.trial_start_date = COALESCE(NEW.trial_start_date, NOW());
    NEW.trial_end_date = NEW.trial_start_date + INTERVAL '15 days';
    NEW.trial_days_remaining = 15;
  END IF;

  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة تسجيل النشاط
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id UUID,
  p_action_type TEXT,
  p_action_details JSONB DEFAULT NULL,
  p_device_id TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_admin_user_id UUID DEFAULT NULL,
  p_admin_reason TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO activity_log (
    user_id, action_type, action_details,
    device_id, ip_address, admin_user_id, admin_reason
  ) VALUES (
    p_user_id, p_action_type, p_action_details,
    p_device_id, p_ip_address, p_admin_user_id, p_admin_reason
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة الإحصائيات الفورية
CREATE OR REPLACE FUNCTION get_live_stats()
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_users', (SELECT COUNT(*) FROM user_accounts),
    'active_users', (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'active'),
    'trial_users', (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'trial'),
    'suspended_users', (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'suspended'),
    'expired_users', (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'expired'),
    'online_users', (SELECT COUNT(DISTINCT user_id) FROM active_sessions WHERE is_active = true AND last_heartbeat > NOW() - INTERVAL '5 minutes'),
    'total_devices', (SELECT COUNT(*) FROM user_devices WHERE device_status = 'active'),
    'blocked_devices', (SELECT COUNT(*) FROM user_devices WHERE device_status = 'blocked'),
    'active_codes', (SELECT COUNT(*) FROM activation_codes WHERE code_status = 'active'),
    'used_codes', (SELECT COUNT(*) FROM activation_codes WHERE code_status = 'used'),
    'total_sessions_today', (SELECT COUNT(*) FROM active_sessions WHERE session_start::date = CURRENT_DATE)
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تنظيف الجلسات المنتهية
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
  cleaned_count INTEGER;
BEGIN
  -- تحديد الجلسات المنتهية (لم ترسل heartbeat لأكثر من 10 دقائق)
  UPDATE active_sessions
  SET is_active = false
  WHERE is_active = true
    AND last_heartbeat < NOW() - INTERVAL '10 minutes';

  GET DIAGNOSTICS cleaned_count = ROW_COUNT;

  -- حذف الجلسات القديمة (أكثر من 7 أيام)
  DELETE FROM active_sessions
  WHERE session_start < NOW() - INTERVAL '7 days';

  RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ✅ دالة إنشاء حساب تجريبي جديد مع تواريخ واضحة
CREATE OR REPLACE FUNCTION create_trial_account(
  p_user_id UUID,
  p_display_name TEXT,
  p_email TEXT DEFAULT NULL,
  p_trial_days INTEGER DEFAULT 15
)
RETURNS JSONB AS $$
DECLARE
  trial_start TIMESTAMPTZ := NOW();
  trial_end TIMESTAMPTZ := NOW() + (p_trial_days || ' days')::INTERVAL;
  result JSONB;
BEGIN
  -- إدراج الحساب الجديد
  INSERT INTO user_accounts (
    user_id,
    display_name,
    email,
    account_status,
    subscription_type,
    trial_days_remaining,
    trial_start_date,
    trial_end_date,
    max_devices,
    created_by
  ) VALUES (
    p_user_id,
    p_display_name,
    p_email,
    'trial',
    'trial',
    p_trial_days,
    trial_start,
    trial_end,
    3,
    p_user_id
  );

  -- تسجيل النشاط
  PERFORM log_user_activity(
    p_user_id,
    'account_created',
    jsonb_build_object(
      'account_type', 'trial',
      'trial_days', p_trial_days,
      'trial_start', trial_start,
      'trial_end', trial_end
    )
  );

  -- إرجاع معلومات الحساب
  SELECT jsonb_build_object(
    'user_id', p_user_id,
    'account_status', 'trial',
    'trial_start_date', trial_start,
    'trial_end_date', trial_end,
    'trial_days_remaining', p_trial_days,
    'max_devices', 3,
    'created_at', NOW()
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ✅ دالة فحص وتحديث حالة الفترة التجريبية
CREATE OR REPLACE FUNCTION check_trial_status(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  account_record RECORD;
  days_remaining INTEGER;
  is_expired BOOLEAN := false;
  result JSONB;
BEGIN
  -- جلب بيانات الحساب
  SELECT * INTO account_record
  FROM user_accounts
  WHERE user_id = p_user_id;

  IF NOT FOUND THEN
    RETURN jsonb_build_object('error', 'Account not found');
  END IF;

  -- فحص حالة الفترة التجريبية
  IF account_record.account_status = 'trial' THEN
    IF account_record.trial_end_date IS NOT NULL THEN
      -- حساب الأيام المتبقية بناءً على التاريخ
      days_remaining := GREATEST(0, EXTRACT(DAY FROM (account_record.trial_end_date - NOW()))::INTEGER);

      -- فحص انتهاء الفترة
      IF account_record.trial_end_date < NOW() THEN
        is_expired := true;
        days_remaining := 0;

        -- تحديث حالة الحساب
        UPDATE user_accounts
        SET
          account_status = 'expired',
          trial_days_remaining = 0,
          updated_at = NOW()
        WHERE user_id = p_user_id;

        -- تسجيل انتهاء الفترة التجريبية
        PERFORM log_user_activity(
          p_user_id,
          'trial_expired',
          jsonb_build_object(
            'trial_end_date', account_record.trial_end_date,
            'expired_at', NOW()
          )
        );
      ELSE
        -- تحديث الأيام المتبقية
        UPDATE user_accounts
        SET
          trial_days_remaining = days_remaining,
          updated_at = NOW()
        WHERE user_id = p_user_id;
      END IF;
    ELSE
      -- إذا لم يكن هناك تاريخ انتهاء، أنشئه
      UPDATE user_accounts
      SET
        trial_start_date = COALESCE(trial_start_date, created_at),
        trial_end_date = COALESCE(trial_start_date, created_at) + INTERVAL '15 days',
        trial_days_remaining = 15,
        updated_at = NOW()
      WHERE user_id = p_user_id;

      days_remaining := 15;
    END IF;
  END IF;

  -- إرجاع النتيجة
  SELECT jsonb_build_object(
    'user_id', p_user_id,
    'account_status', CASE WHEN is_expired THEN 'expired' ELSE account_record.account_status END,
    'trial_start_date', account_record.trial_start_date,
    'trial_end_date', account_record.trial_end_date,
    'trial_days_remaining', days_remaining,
    'is_expired', is_expired,
    'checked_at', NOW()
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة تحديث الإحصائيات اليومية
CREATE OR REPLACE FUNCTION update_daily_stats()
RETURNS VOID AS $$
DECLARE
  today_date DATE := CURRENT_DATE;
BEGIN
  INSERT INTO daily_stats (
    date,
    total_users,
    active_users,
    trial_users,
    online_users,
    new_users,
    suspended_users,
    codes_used,
    codes_created,
    total_sessions
  ) VALUES (
    today_date,
    (SELECT COUNT(*) FROM user_accounts),
    (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'active'),
    (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'trial'),
    (SELECT COUNT(DISTINCT user_id) FROM active_sessions WHERE is_active = true),
    (SELECT COUNT(*) FROM user_accounts WHERE created_at::date = today_date),
    (SELECT COUNT(*) FROM user_accounts WHERE account_status = 'suspended'),
    (SELECT COUNT(*) FROM activation_codes WHERE used_at::date = today_date),
    (SELECT COUNT(*) FROM activation_codes WHERE created_at::date = today_date),
    (SELECT COUNT(*) FROM active_sessions WHERE session_start::date = today_date)
  )
  ON CONFLICT (date)
  DO UPDATE SET
    total_users = EXCLUDED.total_users,
    active_users = EXCLUDED.active_users,
    trial_users = EXCLUDED.trial_users,
    online_users = EXCLUDED.online_users,
    suspended_users = EXCLUDED.suspended_users,
    codes_used = EXCLUDED.codes_used,
    codes_created = EXCLUDED.codes_created,
    total_sessions = EXCLUDED.total_sessions;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================================================================
-- 9. تطبيق الـ Triggers
-- ===================================================================

-- Triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON user_accounts;
CREATE TRIGGER update_user_accounts_updated_at
  BEFORE UPDATE ON user_accounts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_devices_updated_at ON user_devices;
CREATE TRIGGER update_user_devices_updated_at
  BEFORE UPDATE ON user_devices
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger لتحديث حالة المستخدم
DROP TRIGGER IF EXISTS update_user_status_trigger ON user_accounts;
CREATE TRIGGER update_user_status_trigger
  BEFORE INSERT OR UPDATE ON user_accounts
  FOR EACH ROW EXECUTE FUNCTION update_user_status();

-- ===================================================================
-- 10. Row Level Security (RLS)
-- ===================================================================

-- تفعيل RLS على جميع الجداول
ALTER TABLE user_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE active_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE activation_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_stats ENABLE ROW LEVEL SECURITY;

-- حذف السياسات القديمة
DROP POLICY IF EXISTS "Users can view own account" ON user_accounts;
DROP POLICY IF EXISTS "Users can update own account" ON user_accounts;
DROP POLICY IF EXISTS "Admins can manage all accounts" ON user_accounts;

DROP POLICY IF EXISTS "Users can view own devices" ON user_devices;
DROP POLICY IF EXISTS "Users can manage own devices" ON user_devices;
DROP POLICY IF EXISTS "Admins can manage all devices" ON user_devices;

DROP POLICY IF EXISTS "Users can view own sessions" ON active_sessions;
DROP POLICY IF EXISTS "Users can manage own sessions" ON active_sessions;
DROP POLICY IF EXISTS "Admins can manage all sessions" ON active_sessions;

DROP POLICY IF EXISTS "Admins can manage codes" ON activation_codes;
DROP POLICY IF EXISTS "Users can view available codes" ON activation_codes;

DROP POLICY IF EXISTS "Admins can view all logs" ON activity_log;
DROP POLICY IF EXISTS "Users can view own logs" ON activity_log;

DROP POLICY IF EXISTS "Admins can view stats" ON daily_stats;

-- ===================================================================
-- 11. سياسات الأمان الجديدة
-- ===================================================================

-- سياسات user_accounts
CREATE POLICY "Users can view own account" ON user_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own account" ON user_accounts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all accounts" ON user_accounts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- سياسات user_devices
CREATE POLICY "Users can view own devices" ON user_devices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own devices" ON user_devices
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all devices" ON user_devices
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- سياسات active_sessions
CREATE POLICY "Users can view own sessions" ON active_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own sessions" ON active_sessions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all sessions" ON active_sessions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- سياسات activation_codes
CREATE POLICY "Admins can manage codes" ON activation_codes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

CREATE POLICY "Users can view available codes" ON activation_codes
  FOR SELECT USING (code_status = 'active');

-- سياسات activity_log
CREATE POLICY "Admins can view all logs" ON activity_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

CREATE POLICY "Users can view own logs" ON activity_log
  FOR SELECT USING (auth.uid() = user_id);

-- سياسات daily_stats
CREATE POLICY "Admins can view stats" ON daily_stats
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- ===================================================================
-- 12. منح الصلاحيات
-- ===================================================================

GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- منح صلاحيات تنفيذ الدوال
GRANT EXECUTE ON FUNCTION log_user_activity TO authenticated;
GRANT EXECUTE ON FUNCTION get_live_stats TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_sessions TO authenticated;
GRANT EXECUTE ON FUNCTION update_daily_stats TO authenticated;

-- ✅ منح صلاحيات للدوال الجديدة
GRANT EXECUTE ON FUNCTION create_trial_account TO authenticated;
GRANT EXECUTE ON FUNCTION check_trial_status TO authenticated;

-- ===================================================================
-- 13. بيانات تجريبية للاختبار
-- ===================================================================

-- إدراج مدير تجريبي (يجب تعديل user_id ليطابق مستخدم حقيقي)
-- INSERT INTO user_accounts (
--   display_name, email, account_status, subscription_type,
--   subscription_start, subscription_end, max_devices
-- ) VALUES (
--   'مدير النظام', '<EMAIL>', 'active', 'annual',
--   NOW(), NOW() + INTERVAL '1 year', 10
-- );

-- إدراج أكواد تفعيل تجريبية
INSERT INTO activation_codes (code, code_type, duration_days, description) VALUES
('TRIAL30-2025', 'trial_extension', 30, 'كود تمديد تجريبي 30 يوم'),
('MONTH01-2025', 'monthly', 30, 'اشتراك شهري'),
('QUARTER01-2025', 'quarterly', 90, 'اشتراك ربع سنوي'),
('ANNUAL01-2025', 'annual', 365, 'اشتراك سنوي');

-- تحديث الإحصائيات الأولية
SELECT update_daily_stats();

-- ===================================================================
-- انتهاء الملف
-- ===================================================================

COMMIT;
