import 'package:flutter/material.dart';
import '../data/subscriber_model.dart';
import '../../../db_helper.dart';
import '../data/transaction_model.dart';
import 'transactions_log_screen.dart';
import 'renew_subscription_bottom_sheet.dart';
import '../domain/subscribers_repository_impl.dart';
import '../data/subscribers_storage_impl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../utils/api_helper.dart';
import 'edit_subscriber_screen.dart';

class SubscriberDetailsScreen extends StatefulWidget {
  final Subscriber subscriber;
  final dynamic repository;

  const SubscriberDetailsScreen({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<SubscriberDetailsScreen> createState() =>
      _SubscriberDetailsScreenState();
}

class _SubscriberDetailsScreenState extends State<SubscriberDetailsScreen> {
  late Subscriber _subscriber;
  bool _loading = false;
  String? _error;
  bool _isProcessingFinancialOperation =
      false; // لمنع إعادة التحميل أثناء العمليات المالية

  @override
  void initState() {
    super.initState();
    _subscriber = widget.subscriber;
    // تحديث البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchLatestSubscriber();
    });
  }

  Future<void> _fetchLatestSubscriber() async {
    if (!mounted || _isProcessingFinancialOperation) return;

    try {
      // جلب أحدث بيانات المشترك من قاعدة البيانات مباشرة
      if (_subscriber.id != null) {
        final updatedSubscriber = await DBHelper.instance.getSubscriberById(
          _subscriber.id!,
        );

        if (updatedSubscriber != null &&
            mounted &&
            !_isProcessingFinancialOperation) {
          // تحديث سعر الباقة من جدول الباقات
          final updatedSubscriberWithPrice = await _updateSubscriptionPrice(
            updatedSubscriber,
          );

          setState(() {
            _subscriber = updatedSubscriberWithPrice;
          });

          debugPrint(
            '[SUBSCRIBER_DETAILS] تم تحديث بيانات المشترك - الدين الحالي: ${_subscriber.totalDebt}, السعر المحدث: ${_subscriber.subscriptionPrice}',
          );
        }
      }
    } catch (e) {
      debugPrint('[SUBSCRIBER_DETAILS] خطأ في تحديث بيانات المشترك: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث البيانات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// عرض خيارات عنوان IP (تعديل أو فتح في المتصفح)
  Future<void> _showIPOptionsDialog(String currentIP) async {
    if (currentIP == 'غير محدد') {
      // إذا لم يكن هناك IP، اعرض خيار التعديل فقط
      _showEditIPDialog('');
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final colorScheme = Theme.of(context).colorScheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.language, color: colorScheme.primary),
              const SizedBox(width: 8),
              const Text('خيارات عنوان IP'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'عنوان IP الحالي:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  currentIP,
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 16,
                    color: colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text('اختر العملية المطلوبة:'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _showEditIPDialog(currentIP);
              },
              icon: const Icon(Icons.edit),
              label: const Text('تعديل'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _openIPInBrowser(currentIP);
              },
              icon: const Icon(Icons.open_in_browser),
              label: const Text('فتح في المتصفح'),
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار تعديل عنوان IP
  Future<void> _showEditIPDialog(String currentIP) async {
    final TextEditingController ipController = TextEditingController(
      text: currentIP,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final colorScheme = Theme.of(context).colorScheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.edit, color: colorScheme.primary),
              const SizedBox(width: 8),
              const Text('تعديل عنوان IP'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('أدخل عنوان IP الجديد:'),
              const SizedBox(height: 12),
              TextField(
                controller: ipController,
                decoration: InputDecoration(
                  hintText: 'مثال: *************',
                  helperText: 'اتركه فارغاً لحذف IP المخصص',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.language, color: colorScheme.primary),
                  suffixIcon: IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    onPressed: () => ipController.clear(),
                    tooltip: 'مسح النص',
                  ),
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(fontFamily: 'monospace'),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withValues(
                    alpha: 0.5,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'ملاحظات:',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '• إذا أدخلت عنوان IP، سيتم حفظه ولن يتغير عند المزامنة',
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '• إذا تركت الحقل فارغاً، سيتم جلب IP تلقائياً من المزامنة',
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newIP = ipController.text.trim();
                Navigator.of(context).pop();
                await _saveCustomIP(newIP);
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  /// حفظ عنوان IP المخصص في قاعدة البيانات
  Future<void> _saveCustomIP(String newIP) async {
    try {
      final db = await DBHelper.instance.database;

      // تنظيف النص المدخل
      final cleanIP = newIP.trim();

      // تحديد ما إذا كان IP مخصص أم لا
      final isCustomIP = cleanIP.isNotEmpty;
      final ipToSave = cleanIP.isEmpty ? null : cleanIP;

      // تحديث IP في قاعدة البيانات
      await db.update(
        'subscribers',
        {
          'ip': ipToSave,
          'custom_ip': isCustomIP
              ? 1
              : 0, // 1 = مخصص، 0 = يمكن تحديثه من المزامنة
        },
        where: 'id = ?',
        whereArgs: [_subscriber.id],
      );

      // تحديث الواجهة
      if (mounted) {
        setState(() {
          _subscriber = _subscriber.copyWith(ip: ipToSave);
        });

        final message = isCustomIP
            ? 'تم حفظ عنوان IP: $cleanIP'
            : 'تم حذف عنوان IP - سيتم جلبه من المزامنة';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.green),
        );
      }

      if (isCustomIP) {
        debugPrint(
          '[IP_EDIT] تم حفظ IP مخصص للمشترك ${_subscriber.user}: $cleanIP',
        );
      } else {
        debugPrint(
          '[IP_EDIT] تم حذف IP المخصص للمشترك ${_subscriber.user} - سيتم جلبه من المزامنة',
        );
      }
    } catch (e) {
      debugPrint('[IP_EDIT] خطأ في حفظ IP: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ عنوان IP: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// فتح عنوان IP في المتصفح
  Future<void> _openIPInBrowser(String ip) async {
    try {
      if (ip.isEmpty || ip == 'غير محدد') {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يوجد عنوان IP صالح'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // فحص حالة اتصال المشترك قبل فتح المتصفح
      final isOnline = await _checkSubscriberOnlineStatus();

      if (!isOnline) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('المشترك ${_subscriber.name} غير متصل حالياً'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'فتح رغم ذلك',
                textColor: Colors.white,
                onPressed: () => _forceOpenIPInBrowser(ip),
              ),
            ),
          );
        }
        return;
      }

      // إضافة http:// إذا لم يكن موجوداً
      String url = ip;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://$ip';
      }

      final uri = Uri.parse(url);
      final launched = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تعذر فتح المتصفح'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        debugPrint('[IP_BROWSER] تم فتح IP في المتصفح: $url');
      }
    } catch (e) {
      debugPrint('[IP_BROWSER] خطأ في فتح IP في المتصفح: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح المتصفح: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// فحص حالة اتصال المشترك
  Future<bool> _checkSubscriberOnlineStatus() async {
    try {
      // فحص حالة الاتصال من قاعدة البيانات المحلية أولاً
      if (_subscriber.onlineStatus == 1) {
        debugPrint(
          '[ONLINE_CHECK] المشترك ${_subscriber.user} متصل حسب قاعدة البيانات',
        );
        return true;
      }

      // إذا لم يكن متصلاً حسب قاعدة البيانات، تحقق من API Online
      debugPrint(
        '[ONLINE_CHECK] فحص حالة الاتصال من السيرفر للمشترك: ${_subscriber.user}',
      );

      final db = await DBHelper.instance.database;
      final boardResult = await db.query('boards', limit: 1);

      if (boardResult.isEmpty) {
        debugPrint('[ONLINE_CHECK] لا توجد لوحة مكونة');
        return false;
      }

      final board = boardResult.first;

      // استخدام دالة fetchOnlineUsers للتحقق من الاتصال
      final onlineUsers = await fetchOnlineUsers(board: board);

      final isOnline = onlineUsers.any(
        (user) => user['username'] == _subscriber.user,
      );

      debugPrint('[ONLINE_CHECK] حالة اتصال ${_subscriber.user}: $isOnline');
      return isOnline;
    } catch (e) {
      debugPrint('[ONLINE_CHECK] خطأ في فحص حالة الاتصال: $e');
      // في حالة الخطأ، اعتبر المشترك متصل لتجنب منع الوصول
      return true;
    }
  }

  /// فتح IP في المتصفح بالقوة (بدون فحص الاتصال)
  Future<void> _forceOpenIPInBrowser(String ip) async {
    try {
      // إضافة http:// إذا لم يكن موجوداً
      String url = ip;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://$ip';
      }

      final uri = Uri.parse(url);
      final launched = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تعذر فتح المتصفح'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        debugPrint('[IP_BROWSER] تم فتح IP في المتصفح بالقوة: $url');
      }
    } catch (e) {
      debugPrint('[IP_BROWSER] خطأ في فتح IP بالقوة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح المتصفح: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// الحصول على سعر البيع الفعال (مخصص أو افتراضي)
  double _getEffectiveSellPrice(Map<String, dynamic> subscription) {
    final customPrice = subscription['custom_sell_price'];
    if (customPrice != null && customPrice > 0) {
      return (customPrice is num)
          ? customPrice.toDouble()
          : double.tryParse(customPrice.toString()) ?? 0.0;
    }

    final sellPrice = subscription['sellPrice'];
    if (sellPrice != null) {
      return (sellPrice is num)
          ? sellPrice.toDouble()
          : double.tryParse(sellPrice.toString()) ?? 0.0;
    }

    return 0.0;
  }

  /// تحديث سعر الباقة من جدول الباقات (مع دعم السعر المخصص)
  Future<Subscriber> _updateSubscriptionPrice(Subscriber subscriber) async {
    try {
      // جلب السعر الحالي للباقة من جدول الباقات
      final subscriptions = await DBHelper.instance.getAllSubscriptions();
      final matchingSubscription = subscriptions.firstWhere(
        (sub) => sub['name'] == subscriber.subscriptionType,
        orElse: () => throw Exception('لم يتم العثور على الباقة'),
      );

      // استخدام السعر المخصص إذا كان موجوداً، وإلا السعر الافتراضي
      final currentPrice = _getEffectiveSellPrice(matchingSubscription);

      // إذا كان السعر مختلفاً، قم بتحديثه
      if (currentPrice != subscriber.subscriptionPrice) {
        debugPrint(
          '[PRICE_UPDATE] تحديث سعر الباقة من ${subscriber.subscriptionPrice} إلى $currentPrice (${matchingSubscription['custom_sell_price'] != null ? 'مخصص' : 'افتراضي'})',
        );

        final updatedSubscriber = subscriber.copyWith(
          subscriptionPrice: currentPrice,
        );

        // حفظ السعر المحدث في قاعدة البيانات
        await DBHelper.instance.updateSubscriber(updatedSubscriber);

        return updatedSubscriber;
      }

      // إذا كان السعر نفسه، أرجع المشترك كما هو
      return subscriber;
    } catch (e) {
      debugPrint('[PRICE_UPDATE] خطأ في تحديث سعر الباقة: $e');
      // في حالة الخطأ، أرجع المشترك كما هو
      return subscriber;
    }
  }

  Color _getStatusColor(Subscriber subscriber) {
    // تحديد اللون بناءً على حالة المشترك
    if (subscriber.isDeletedStatus) {
      return Colors.grey;
    } else if (subscriber.endDate.isBefore(DateTime.now())) {
      return Colors.red; // منتهي
    } else if (subscriber.endDate.difference(DateTime.now()).inDays <= 3) {
      return Colors.orange; // قريب الانتهاء
    } else if (subscriber.totalDebt > 0) {
      return Colors.purple; // عليه ديون
    } else {
      return Colors.green; // فعال
    }
  }

  String _getStatusText(Subscriber subscriber) {
    // تحديد النص بناءً على حالة المشترك
    if (subscriber.isDeletedStatus) {
      return 'محذوف';
    } else if (subscriber.endDate.isBefore(DateTime.now())) {
      return 'منتهي';
    } else if (subscriber.endDate.difference(DateTime.now()).inDays <= 3) {
      return 'قريب الانتهاء';
    } else if (subscriber.totalDebt > 0) {
      return 'عليه ديون';
    } else {
      return 'فعال';
    }
  }

  // دالة للتحقق من إمكانية التعديل
  bool _canEdit() {
    if (_subscriber.isManualSubscriber) {
      return true; // المشتركين اليدويين يمكن تعديلهم دائماً
    } else if (_subscriber.isSasSubscriber ||
        _subscriber.isEarthlinkSubscriber) {
      return false; // مشتركين SAS و Earthlink لا يمكن تعديلهم
    } else {
      // للمشتركين القدامى بدون source_type
      return _subscriber.isDeletedStatus;
    }
  }

  // دالة للتحقق من إمكانية الحذف
  bool _canDelete() {
    if (_subscriber.isManualSubscriber) {
      return _subscriber.totalDebt <=
          0; // المشتركين اليدويين يمكن حذفهم إذا لم يكن عليهم ديون
    } else if (_subscriber.isSasSubscriber ||
        _subscriber.isEarthlinkSubscriber) {
      return false; // مشتركين SAS و Earthlink لا يمكن حذفهم
    } else {
      // للمشتركين القدامى بدون source_type
      return _subscriber.isDeletedStatus && _subscriber.totalDebt <= 0;
    }
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'transactions':
        _showTransactionsLog();
        break;
      case 'edit':
        _editSubscriber();
        break;
      case 'delete':
        _deleteSubscriber();
        break;
    }
  }

  Future<void> _showTransactionsLog() async {
    try {
      // جلب سجل العمليات الخاص بهذا المشترك من قاعدة البيانات
      final allTransactions = await DBHelper.instance.getAllTransactions();
      final subscriberTransactions = allTransactions
          .where((transaction) => transaction.subscriberId == _subscriber.id)
          .toList();

      if (!mounted) return;

      if (subscriberTransactions.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا توجد عمليات مسجلة لهذا المشترك')),
        );
        return;
      }

      // إظهار حوار بسجل العمليات
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('سجل العمليات - ${_subscriber.name}'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: subscriberTransactions.length,
                itemBuilder: (context, index) {
                  final transaction = subscriberTransactions[index];
                  return _buildTransactionItem(transaction);
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // الانتقال إلى شاشة سجل العمليات الكاملة مع تصفية هذا المشترك
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => TransactionsLogScreen(
                        initialSubscriberId: _subscriber.id,
                      ),
                    ),
                  );
                },
                child: const Text('عرض التفاصيل'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في جلب سجل العمليات: $e')));
      }
    }
  }

  /// بناء عنصر العملية في القائمة
  Widget _buildTransactionItem(Transaction transaction) {
    // تحديد الأيقونة واللون حسب نوع العملية
    IconData icon;
    Color color;
    String typeText;

    switch (transaction.type) {
      case TransactionType.renewal:
        icon = Icons.refresh;
        color = Colors.green;
        typeText = 'تجديد';
        break;
      case TransactionType.payDebt:
        icon = Icons.payment;
        color = Colors.blue;
        typeText = 'تسديد';
        break;
      case TransactionType.addDebt:
        icon = Icons.add_circle;
        color = Colors.orange;
        typeText = 'إضافة دين';
        break;
      case TransactionType.addCredit:
        icon = Icons.account_balance_wallet;
        color = Colors.purple;
        typeText = 'إضافة رصيد';
        break;
    }

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          transaction.description,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(typeText, style: TextStyle(color: color, fontSize: 12)),
        trailing: Text(
          _formatDate(transaction.date),
          style: const TextStyle(fontSize: 11, color: Colors.grey),
        ),
      ),
    );
  }

  /// إعادة تحميل بيانات المشترك من قاعدة البيانات
  Future<void> _refreshSubscriber() async {
    if (_subscriber.id != null) {
      final updatedSubscriber = await DBHelper.instance.getSubscriberById(
        _subscriber.id!,
      );
      if (updatedSubscriber != null) {
        setState(() {
          _subscriber = updatedSubscriber;
        });
      }
    }
  }

  /// التنقل إلى شاشة التعديل
  Future<void> _navigateToEditScreen() async {
    try {
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => EditSubscriberScreen(
            subscriber: _subscriber,
            repository: widget.repository,
          ),
        ),
      );

      // إذا تم التعديل بنجاح، أعد تحميل بيانات المشترك
      if (result == true && mounted) {
        await _refreshSubscriber();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث بيانات المشترك بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح شاشة التعديل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _editSubscriber() {
    // فحص نوع المشترك أولاً
    if (_subscriber.isSasSubscriber) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'لا يمكن تعديل مشتركين SAS - يتم إدارتهم من خلال السيرفر فقط',
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_subscriber.isEarthlinkSubscriber) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تعديل مشتركين Earthlink حالياً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // المشتركين اليدويين يمكن تعديلهم دائماً
    if (_subscriber.isManualSubscriber) {
      // الانتقال لشاشة التعديل
      _navigateToEditScreen();
      return;
    }

    // للمشتركين القدامى بدون source_type (افتراضياً SAS)
    if (!_subscriber.isDeletedStatus) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن تعديل المشترك إلا إذا كان محذوفاً من السيرفر'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // الانتقال لشاشة التعديل (إذا كانت متوفرة)
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التعديل قيد التطوير')));
  }

  void _deleteSubscriber() {
    // فحص نوع المشترك أولاً
    if (_subscriber.isSasSubscriber) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'لا يمكن حذف مشتركين SAS - يتم إدارتهم من خلال السيرفر فقط',
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_subscriber.isEarthlinkSubscriber) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن حذف مشتركين Earthlink حالياً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // المشتركين اليدويين يمكن حذفهم دائماً (بشرط عدم وجود ديون)
    if (_subscriber.isManualSubscriber) {
      if (_subscriber.totalDebt > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'لا يمكن حذف المشترك لأن عليه ديون. يجب تسديد الديون أولاً',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
      // يمكن حذف المشترك اليدوي مباشرة
    } else {
      // للمشتركين القدامى بدون source_type (افتراضياً SAS)
      if (!_subscriber.isDeletedStatus) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يمكن حذف المشترك إلا إذا كان محذوفاً من السيرفر'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      if (_subscriber.totalDebt > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'لا يمكن حذف المشترك لأن عليه ديون. يجب تسديد الديون أولاً',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    // إظهار حوار تأكيد الحذف
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف المشترك "${_subscriber.name}"؟\nهذا الإجراء لا يمكن التراجع عنه.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _confirmDelete();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _confirmDelete() async {
    try {
      // حذف المشترك من قاعدة البيانات
      await DBHelper.instance.deleteSubscriber(_subscriber.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المشترك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للشاشة السابقة مع إشارة التحديث
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المشترك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إضافة دين للمشترك
  Future<void> _showAddDebtDialog() async {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController noteController = TextEditingController();

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إضافة دين'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ (دينار عراقي)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.monetization_on),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظة (اختياري)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amount = double.tryParse(amountController.text);
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال مبلغ صحيح'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();
                await _addDebt(amount, noteController.text.trim());
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  /// إضافة دين للمشترك مع منطق السداد الذكي من المحفظة
  Future<void> _addDebt(double amount, String note) async {
    try {
      setState(() {
        _loading = true;
        _isProcessingFinancialOperation = true;
      });

      // استخراج رصيد المحفظة الحالي من العمود المخصص
      final currentWallet = _subscriber.walletBalance;
      final oldDebt = _subscriber.totalDebt;

      debugPrint(
        '[ADD_DEBT] بدء إضافة دين: $amount، الدين الحالي: $oldDebt، رصيد المحفظة: $currentWallet',
      );

      double finalDebt = oldDebt + amount;
      double finalWallet = currentWallet;
      String resultMessage = '';
      List<String> transactionDescriptions = [];

      // منطق السداد الذكي
      if (currentWallet > 0) {
        if (currentWallet >= amount) {
          // رصيد المحفظة يكفي لسداد الدين الجديد بالكامل
          finalWallet = currentWallet - amount;
          finalDebt = oldDebt; // الدين يبقى كما هو (لم يتم إضافة دين جديد)

          resultMessage =
              'تم سداد الدين (${amount.toStringAsFixed(0)} د.ع) تلقائياً من المحفظة. الرصيد المتبقي: ${finalWallet.toStringAsFixed(0)} د.ع';
          transactionDescriptions.add(
            'سداد تلقائي من المحفظة${note.isNotEmpty ? ': $note' : ''}',
          );

          debugPrint(
            '[ADD_DEBT] سداد كامل من المحفظة - المحفظة الجديدة: $finalWallet، الدين النهائي: $finalDebt',
          );
        } else {
          // رصيد المحفظة يكفي لسداد جزء من الدين
          final paidFromWallet = currentWallet;
          final remainingDebt = amount - currentWallet;

          finalWallet = 0;
          finalDebt = oldDebt + remainingDebt;

          resultMessage =
              'تم سداد ${paidFromWallet.toStringAsFixed(0)} د.ع من المحفظة. الدين المتبقي: ${remainingDebt.toStringAsFixed(0)} د.ع تم إضافته للحساب';
          transactionDescriptions.add(
            'سداد جزئي من المحفظة ${paidFromWallet.toStringAsFixed(0)} د.ع${note.isNotEmpty ? ': $note' : ''}',
          );
          transactionDescriptions.add(
            'إضافة دين متبقي ${remainingDebt.toStringAsFixed(0)} د.ع${note.isNotEmpty ? ': $note' : ''}',
          );

          debugPrint(
            '[ADD_DEBT] سداد جزئي من المحفظة - المدفوع: $paidFromWallet، الدين المضاف: $remainingDebt، الدين النهائي: $finalDebt',
          );
        }
      } else {
        // لا يوجد رصيد في المحفظة - إضافة الدين كاملاً
        resultMessage = 'تم إضافة دين بقيمة ${amount.toStringAsFixed(0)} د.ع';
        transactionDescriptions.add(
          'إضافة دين${note.isNotEmpty ? ': $note' : ''}',
        );

        debugPrint('[ADD_DEBT] لا يوجد رصيد محفظة - الدين النهائي: $finalDebt');
      }

      // تحديث بيانات المشترك مع رصيد المحفظة الجديد
      final updatedSubscriber = _subscriber.copyWith(
        totalDebt: finalDebt,
        walletBalance: finalWallet,
      );
      await DBHelper.instance.updateSubscriber(updatedSubscriber);

      // تحديث الحالة المحلية فوراً
      setState(() {
        _subscriber = updatedSubscriber;
      });

      // تسجيل المعاملات بدقة
      for (String description in transactionDescriptions) {
        if (description.contains('سداد تلقائي من المحفظة')) {
          // سداد كامل من المحفظة
          await _logTransaction(
            type: 'debt_payment',
            amount: amount,
            description: description,
          );
        } else if (description.contains('سداد جزئي من المحفظة')) {
          // سداد جزئي من المحفظة
          await _logTransaction(
            type: 'debt_payment',
            amount: currentWallet,
            description: description,
          );
        } else if (description.contains('إضافة دين متبقي')) {
          // إضافة الدين المتبقي بعد السداد الجزئي
          await _logTransaction(
            type: 'debt_added',
            amount: amount - currentWallet,
            description: description,
          );
        } else if (description.contains('إضافة دين')) {
          // إضافة دين كامل (بدون سداد من المحفظة)
          await _logTransaction(
            type: 'debt_added',
            amount: amount,
            description: description,
          );
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(resultMessage),
            backgroundColor: finalDebt <= oldDebt
                ? Colors.green
                : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }

      debugPrint(
        '[ADD_DEBT] العملية مكتملة - الدين النهائي: $finalDebt، المحفظة النهائية: $finalWallet',
      );
    } catch (e) {
      debugPrint('[ADD_DEBT] خطأ في العملية: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الدين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _loading = false;
          _isProcessingFinancialOperation = false;
        });
      }
    }
  }

  /// عرض حوار تسديد/دفع مبلغ
  Future<void> _showPaymentDialog() async {
    final TextEditingController amountController = TextEditingController();
    final TextEditingController noteController = TextEditingController();
    final bool hasDebt = _subscriber.totalDebt > 0;

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(_getPaymentDialogTitle()),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (hasDebt) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.red),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'إجمالي الديون: ${_subscriber.totalDebt.toStringAsFixed(0)} دينار',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                // عرض رصيد المحفظة الحالي إذا كان موجوداً
                if (!hasDebt && _getWalletBalance() > 0) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.account_balance_wallet,
                          color: Colors.blue,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'الرصيد الحالي: ${_getWalletBalance().toStringAsFixed(0)} دينار',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                TextField(
                  controller: amountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: _getPaymentFieldLabel(),
                    border: const OutlineInputBorder(),
                    prefixIcon: Icon(_getPaymentButtonIcon()),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظة (اختياري)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final amount = double.tryParse(amountController.text);
                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('يرجى إدخال مبلغ صحيح'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();
                if (hasDebt) {
                  await _payDebt(amount, noteController.text.trim());
                } else {
                  await _addCredit(amount, noteController.text.trim());
                }
              },
              child: Text(_getPaymentConfirmButtonText()),
            ),
          ],
        );
      },
    );
  }

  /// تسديد دين مع إمكانية إضافة رصيد
  Future<void> _payDebt(double amount, String note) async {
    try {
      setState(() {
        _loading = true;
        _isProcessingFinancialOperation = true;
      });

      final currentDebt = _subscriber.totalDebt;

      // إذا كان المبلغ أكبر من الدين، اعرض تنبيه
      if (amount > currentDebt) {
        final excess = amount - currentDebt;
        final confirmed = await _showExcessPaymentDialog(
          debtAmount: currentDebt,
          paidAmount: amount,
          excessAmount: excess,
        );

        if (!confirmed) {
          setState(() => _loading = false);
          return;
        }

        // تسديد كامل الدين وإضافة الباقي كرصيد
        await _processExcessPayment(
          debtAmount: currentDebt,
          excessAmount: excess,
          note: note,
        );
      } else {
        // تسديد عادي
        await _processNormalPayment(amount, note);
      }

      // لا نحتاج لاستدعاء _fetchLatestSubscriber لأننا نحدث الحالة مباشرة في العمليات
      debugPrint(
        '[PAY_DEBT] تمت العملية بنجاح - الدين الحالي: ${_subscriber.totalDebt}',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسديد الدين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _loading = false;
          _isProcessingFinancialOperation = false;
        });
      }
    }
  }

  /// عرض حوار تأكيد الدفع الزائد
  Future<bool> _showExcessPaymentDialog({
    required double debtAmount,
    required double paidAmount,
    required double excessAmount,
  }) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('تنبيه - مبلغ زائد'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.orange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المبلغ المدخل أكبر من إجمالي الديون:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• إجمالي الديون: ${debtAmount.toStringAsFixed(0)} دينار',
                        ),
                        Text(
                          '• المبلغ المدفوع: ${paidAmount.toStringAsFixed(0)} دينار',
                        ),
                        Text(
                          '• المبلغ الزائد: ${excessAmount.toStringAsFixed(0)} دينار',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'في حالة الموافقة سيتم:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('✓ تسديد كامل الديون'),
                  const Text('✓ إضافة المبلغ الزائد كرصيد في المحفظة'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('موافق'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// معالجة التسديد العادي
  Future<void> _processNormalPayment(double amount, String note) async {
    final oldDebt = _subscriber.totalDebt;

    // حساب المبلغ المتبقي من الدين
    final newDebt = (oldDebt - amount).clamp(0.0, double.infinity);

    debugPrint(
      '[PAYMENT] الدين القديم: $oldDebt، المبلغ المدفوع: $amount، الدين الجديد: $newDebt',
    );

    // تحديث المشترك مع الدين الجديد
    final updatedSubscriber = _subscriber.copyWith(totalDebt: newDebt);

    // حفظ في قاعدة البيانات
    await DBHelper.instance.updateSubscriber(updatedSubscriber);

    // تحديث الحالة المحلية فوراً
    setState(() {
      _subscriber = updatedSubscriber;
    });

    debugPrint(
      '[PAYMENT] تم تحديث قاعدة البيانات والحالة المحلية - الدين الحالي: ${_subscriber.totalDebt}',
    );

    // تحقق من أن التحديث تم بنجاح في قاعدة البيانات
    final verifySubscriber = await DBHelper.instance.getSubscriberById(
      _subscriber.id!,
    );
    if (verifySubscriber != null) {
      debugPrint(
        '[PAYMENT] تحقق من قاعدة البيانات - الدين المحفوظ: ${verifySubscriber.totalDebt}',
      );
      if (verifySubscriber.totalDebt != newDebt) {
        debugPrint('[PAYMENT] تحذير: عدم تطابق الدين في قاعدة البيانات!');
      }
    }

    // تسجيل العملية في سجل العمليات
    await _logTransaction(
      type: 'debt_payment',
      amount: amount,
      description: 'تسديد دين${note.isNotEmpty ? ': $note' : ''}',
    );

    // تحديث سعر الباقة قبل إرسال الرسالة
    final subscriberWithUpdatedPrice = await _updateSubscriptionPrice(
      _subscriber,
    );
    if (subscriberWithUpdatedPrice.subscriptionPrice !=
        _subscriber.subscriptionPrice) {
      setState(() {
        _subscriber = subscriberWithUpdatedPrice;
      });
    }

    // إرسال رسالة واتساب إذا كان مفعلاً في الإعدادات
    await _sendWhatsAppPaymentMessage(amount, newDebt, note);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تسديد ${amount.toStringAsFixed(0)} دينار${newDebt == 0 ? ' - تم سداد جميع الديون' : ''}',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// معالجة التسديد مع رصيد زائد
  Future<void> _processExcessPayment({
    required double debtAmount,
    required double excessAmount,
    required String note,
  }) async {
    debugPrint(
      '[EXCESS_PAYMENT] الدين: $debtAmount، الرصيد الزائد: $excessAmount',
    );

    // تسديد كامل الدين وتحديث الحالة المحلية
    final updatedSubscriber = _subscriber.copyWith(totalDebt: 0.0);
    await DBHelper.instance.updateSubscriber(updatedSubscriber);

    setState(() {
      _subscriber = updatedSubscriber;
    });

    debugPrint(
      '[EXCESS_PAYMENT] تم تسديد كامل الديون - الدين الحالي: ${_subscriber.totalDebt}',
    );

    // إضافة الرصيد الزائد للمحفظة
    final currentWallet = _getWalletBalance();
    final newWallet = currentWallet + excessAmount;
    await _updateWalletBalance(newWallet);

    debugPrint(
      '[EXCESS_PAYMENT] تم إضافة الرصيد الزائد - الرصيد الجديد: $newWallet',
    );

    // تسجيل عملية التسديد
    await _logTransaction(
      type: 'debt_payment',
      amount: debtAmount,
      description: 'تسديد كامل الديون${note.isNotEmpty ? ': $note' : ''}',
    );

    // تسجيل عملية إضافة الرصيد
    await _logTransaction(
      type: 'credit_added',
      amount: excessAmount,
      description: 'إضافة رصيد زائد للمحفظة${note.isNotEmpty ? ': $note' : ''}',
    );

    // تحديث سعر الباقة قبل إرسال الرسالة
    final subscriberWithUpdatedPrice = await _updateSubscriptionPrice(
      _subscriber,
    );
    if (subscriberWithUpdatedPrice.subscriptionPrice !=
        _subscriber.subscriptionPrice) {
      setState(() {
        _subscriber = subscriberWithUpdatedPrice;
      });
    }

    // إرسال رسالة واتساب إذا كان مفعلاً في الإعدادات
    await _sendWhatsAppPaymentMessage(debtAmount + excessAmount, 0.0, note);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم تسديد ${debtAmount.toStringAsFixed(0)} دينار وإضافة ${excessAmount.toStringAsFixed(0)} دينار للمحفظة',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// إضافة رصيد للمشترك
  Future<void> _addCredit(double amount, String note) async {
    try {
      setState(() {
        _loading = true;
        _isProcessingFinancialOperation = true;
      });

      // إضافة الرصيد للمحفظة
      final currentWallet = _getWalletBalance();
      final newWallet = currentWallet + amount;
      await _updateWalletBalance(newWallet);

      // تسجيل العملية في سجل العمليات
      await _logTransaction(
        type: 'credit_added',
        amount: amount,
        description: 'إضافة رصيد${note.isNotEmpty ? ': $note' : ''}',
      );

      // لا نحتاج لاستدعاء _fetchLatestSubscriber لأن إضافة الرصيد لا تؤثر على بيانات المشترك الأساسية
      debugPrint(
        '[ADD_CREDIT] تم إضافة الرصيد بنجاح - الرصيد الجديد: $newWallet',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة ${amount.toStringAsFixed(0)} دينار للرصيد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الرصيد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _loading = false;
          _isProcessingFinancialOperation = false;
        });
      }
    }
  }

  /// تسجيل العملية في سجل العمليات
  Future<void> _logTransaction({
    required String type,
    required double amount,
    required String description,
  }) async {
    try {
      // تحديد نوع العملية
      TransactionType transactionType;
      switch (type) {
        case 'debt_added':
          transactionType = TransactionType.addDebt;
          break;
        case 'debt_payment':
          transactionType = TransactionType.payDebt;
          break;
        case 'credit_added':
          transactionType = TransactionType.addCredit; // إضافة رصيد
          break;
        default:
          transactionType = TransactionType.renewal;
      }

      // إنشاء كائن العملية
      final transaction = Transaction(
        type: transactionType,
        description: description,
        date: DateTime.now(),
        subscriberId: _subscriber.id,
      );

      // تسجيل العملية في قاعدة البيانات
      await DBHelper.instance.insertTransaction(transaction);
      debugPrint('تم تسجيل العملية: $description');
    } catch (e) {
      // تجاهل أخطاء تسجيل العمليات لعدم إيقاف العملية الأساسية
      debugPrint('خطأ في تسجيل العملية: $e');
    }
  }

  /// استخراج رصيد المحفظة من العمود المخصص (النظام الجديد)
  double _getWalletBalance() {
    return _subscriber.walletBalance;
  }

  /// تحديث رصيد المحفظة (النظام الجديد)
  Future<void> _updateWalletBalance(double newBalance) async {
    final updatedSubscriber = _subscriber.copyWith(walletBalance: newBalance);
    await DBHelper.instance.updateSubscriber(updatedSubscriber);

    // تحديث الحالة المحلية
    setState(() {
      _subscriber = updatedSubscriber;
    });

    debugPrint('[WALLET] تم تحديث رصيد المحفظة إلى: $newBalance');
  }

  Widget buildStatusIconsRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildStatusIcon(
          icon: Icons.wifi,
          label: _getOnlineStatus(),
          color: _subscriber.onlineStatus == 1 ? Colors.green : Colors.grey,
        ),
        _buildStatusIcon(
          icon: Icons.schedule,
          label: _getRemainingDays(),
          color: _getStatusColor(_subscriber),
        ),
        _buildFinancialStatusIcon(),
      ],
    );
  }

  /// بناء أيقونة الحالة المالية (رصيد أو دين)
  Widget _buildFinancialStatusIcon() {
    final walletBalance = _getWalletBalance();

    // إذا كان هناك رصيد في المحفظة، اعرضه
    if (walletBalance > 0) {
      return _buildStatusIcon(
        icon: Icons.account_balance_wallet,
        label: 'رصيد: ${walletBalance.toStringAsFixed(0)} د.ع',
        color: Colors.blue,
      );
    }
    // إذا كان هناك ديون، اعرضها
    else if (_subscriber.totalDebt > 0) {
      return _buildStatusIcon(
        icon: Icons.warning,
        label: 'دين: ${_subscriber.totalDebt.toStringAsFixed(0)} د.ع',
        color: Colors.red,
      );
    }
    // إذا لم يكن هناك ديون أو رصيد
    else {
      return _buildStatusIcon(
        icon: Icons.check_circle,
        label: 'لا توجد ديون',
        color: Colors.green,
      );
    }
  }

  /// تحديد لون زر الدفع/التسديد
  Color _getPaymentButtonColor() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return Colors.green; // تسديد دين
    } else if (walletBalance > 0) {
      return Colors.blue; // إضافة للرصيد الموجود
    } else {
      return Colors.orange; // إنشاء رصيد جديد
    }
  }

  /// تحديد أيقونة زر الدفع/التسديد
  IconData _getPaymentButtonIcon() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return Icons.payment; // تسديد دين
    } else if (walletBalance > 0) {
      return Icons.add_circle; // إضافة للرصيد
    } else {
      return Icons.account_balance_wallet; // إنشاء رصيد
    }
  }

  /// تحديد نص زر الدفع/التسديد
  String _getPaymentButtonText() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return 'تسديد مبلغ'; // تسديد دين
    } else if (walletBalance > 0) {
      return 'إضافة للرصيد'; // إضافة للرصيد الموجود
    } else {
      return 'دفع مبلغ'; // إنشاء رصيد جديد
    }
  }

  /// تحديد عنوان حوار الدفع/التسديد
  String _getPaymentDialogTitle() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return 'تسديد مبلغ'; // تسديد دين
    } else if (walletBalance > 0) {
      return 'إضافة للرصيد'; // إضافة للرصيد الموجود
    } else {
      return 'دفع مبلغ'; // إنشاء رصيد جديد
    }
  }

  /// تحديد نص حقل إدخال المبلغ
  String _getPaymentFieldLabel() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return 'مبلغ التسديد (دينار عراقي)'; // تسديد دين
    } else if (walletBalance > 0) {
      return 'مبلغ الإضافة (دينار عراقي)'; // إضافة للرصيد الموجود
    } else {
      return 'المبلغ (دينار عراقي)'; // إنشاء رصيد جديد
    }
  }

  /// تحديد نص زر التأكيد في حوار الدفع
  String _getPaymentConfirmButtonText() {
    final walletBalance = _getWalletBalance();

    if (_subscriber.totalDebt > 0) {
      return 'تسديد'; // تسديد دين
    } else if (walletBalance > 0) {
      return 'إضافة'; // إضافة للرصيد الموجود
    } else {
      return 'دفع'; // إنشاء رصيد جديد
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getRemainingDays() {
    final now = DateTime.now();
    final difference = _subscriber.endDate.difference(now);

    if (difference.inSeconds <= 0) {
      return 'منتهي';
    } else if (difference.inDays == 0) {
      final hours = difference.inHours;
      return '$hours ساعة متبقية';
    } else {
      return '${difference.inDays} يوم متبقي';
    }
  }

  String _getOnlineStatus() {
    return _subscriber.onlineStatus == 1 ? 'متصل' : 'غير متصل';
  }

  Widget _buildStatusIcon({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainCard(
    ColorScheme colorScheme,
    bool isDark,
    Color statusColor,
  ) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // صورة المشترك
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: statusColor.withValues(alpha: 0.18),
                    blurRadius: 24,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 48,
                backgroundColor: Colors.white.withValues(
                  alpha: isDark ? 0.08 : 0.18,
                ),
                child: Icon(Icons.person, color: statusColor, size: 54),
              ),
            ),
            const SizedBox(height: 16),

            // اسم المشترك
            Text(
              _subscriber.name,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
                letterSpacing: 1.2,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),

            // حالة المشترك
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _getStatusText(_subscriber),
                style: TextStyle(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 12),

            // مؤشر نوع المشترك
            _buildSubscriberTypeIndicator(),
            const SizedBox(height: 16),

            // أيقونات الحالة
            buildStatusIconsRow(),
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر نوع المشترك
  Widget _buildSubscriberTypeIndicator() {
    String typeText;
    Color typeColor;
    IconData typeIcon;

    if (_subscriber.isManualSubscriber) {
      typeText = 'مشترك يدوي';
      typeColor = Colors.blue;
      typeIcon = Icons.person_add;
    } else if (_subscriber.isSasSubscriber) {
      typeText = 'مشترك SAS';
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    } else if (_subscriber.isEarthlinkSubscriber) {
      typeText = 'مشترك Earthlink';
      typeColor = Colors.orange;
      typeIcon = Icons.language;
    } else {
      typeText = 'مشترك SAS'; // افتراضي للمشتركين القدامى
      typeColor = Colors.green;
      typeIcon = Icons.cloud;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: typeColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(typeIcon, size: 16, color: typeColor),
          const SizedBox(width: 6),
          Text(
            typeText,
            style: TextStyle(
              color: typeColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل المشترك',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('اسم المستخدم', _subscriber.user, colorScheme),
            _buildPhoneDetailRow('رقم الهاتف', _subscriber.phone, colorScheme),
            _buildDetailRow(
              'نوع الاشتراك',
              _subscriber.subscriptionType,
              colorScheme,
            ),
            _buildSubscriptionPriceRow(colorScheme),
            _buildDetailRow(
              'تاريخ البداية',
              _formatDate(_subscriber.startDate),
              colorScheme,
            ),
            _buildDetailRow(
              'تاريخ الانتهاء',
              _formatDate(_subscriber.endDate),
              colorScheme,
            ),
            _buildClickableIPDetailRow(
              'عنوان IP',
              _subscriber.ip?.isNotEmpty == true ? _subscriber.ip! : 'غير محدد',
              colorScheme,
            ),
            if (_subscriber.notes != null && _subscriber.notes!.isNotEmpty)
              _buildDetailRow('ملاحظات', _subscriber.notes!, colorScheme),
          ],
        ),
      ),
    );
  }

  /// بناء صف سعر الاشتراك مع مؤشر نوع السعر
  Widget _buildSubscriptionPriceRow(ColorScheme colorScheme) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _getSubscriptionDetails(),
      builder: (context, snapshot) {
        final isCustomPrice =
            snapshot.hasData &&
            snapshot.data!['custom_sell_price'] != null &&
            snapshot.data!['custom_sell_price'] > 0;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 100,
                child: Text(
                  'سعر الاشتراك',
                  style: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Row(
                  children: [
                    // حاوية السعر
                    Flexible(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '${_subscriber.subscriptionPrice.toStringAsFixed(0)} د.ع',
                          style: TextStyle(
                            color: colorScheme.onSurface,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // مؤشر نوع السعر بجانب السعر
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 5,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: isCustomPrice
                            ? Colors.blue.withValues(alpha: 0.12)
                            : Colors.grey.withValues(alpha: 0.12),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isCustomPrice
                              ? Colors.blue.withValues(alpha: 0.4)
                              : Colors.grey.withValues(alpha: 0.4),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isCustomPrice
                                ? Icons.diamond_outlined
                                : Icons.receipt_long_outlined,
                            size: 14,
                            color: isCustomPrice
                                ? Colors.blue[700]
                                : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isCustomPrice ? 'مخصص' : 'افتراضي',
                            style: TextStyle(
                              fontSize: 11,
                              color: isCustomPrice
                                  ? Colors.blue[700]
                                  : Colors.grey[600],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// الحصول على تفاصيل الباقة من قاعدة البيانات
  Future<Map<String, dynamic>?> _getSubscriptionDetails() async {
    try {
      final subscriptions = await DBHelper.instance.getAllSubscriptions();
      return subscriptions.firstWhere(
        (sub) => sub['name'] == _subscriber.subscriptionType,
        orElse: () => <String, dynamic>{},
      );
    } catch (e) {
      debugPrint('[SUBSCRIPTION_DETAILS] خطأ في جلب تفاصيل الباقة: $e');
      return null;
    }
  }

  Widget _buildDetailRow(String title, String value, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              title,
              style: TextStyle(
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                value,
                style: TextStyle(
                  color: colorScheme.onSurface,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإجراءات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),

            // أزرار الإجراءات
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loading ? null : _showAddDebtDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.add_circle_outline),
                    label: const Text('اضافة دين'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loading ? null : _showPaymentDialog,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getPaymentButtonColor(),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: Icon(_getPaymentButtonIcon()),
                    label: Text(_getPaymentButtonText()),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  // فتح شاشة تجديد الاشتراك
                  final result = await showModalBottomSheet<bool>(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    builder: (context) => Container(
                      height: MediaQuery.of(context).size.height * 0.9,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: RenewSubscriptionBottomSheet(
                        subscriber: _subscriber,
                        repository: SubscribersRepositoryImpl(
                          SubscribersStorageImpl(),
                        ),
                      ),
                    ),
                  );

                  // إذا تم التجديد بنجاح، أعد تحميل بيانات المشترك
                  if (result == true) {
                    await _fetchLatestSubscriber();
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم تجديد الاشتراك بنجاح'),
                          backgroundColor: Colors.green,
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFB9B9FF),
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.refresh),
                label: const Text('تجديد الاشتراك'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final Color statusColor = _getStatusColor(_subscriber);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية مثل شاشة تسجيل الدخول
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          if (_loading)
            Center(
              child: Card(
                elevation: 0,
                color: colorScheme.surface.withValues(
                  alpha: isDark ? 0.7 : 0.93,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(22),
                ),
                child: const Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(color: Colors.white),
                      SizedBox(height: 16),
                      Text(
                        'جاري تحميل بيانات المشترك...',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            )
          else if (_error != null)
            Center(
              child: Card(
                elevation: 0,
                color: colorScheme.surface.withValues(
                  alpha: isDark ? 0.7 : 0.93,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(22),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'خطأ في تحميل البيانات',
                        style: TextStyle(
                          color: colorScheme.onSurface,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: TextStyle(
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _fetchLatestSubscriber,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(18),
                          ),
                        ),
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              ),
            )
          else
            SafeArea(
              child: RefreshIndicator(
                onRefresh: _fetchLatestSubscriber,
                color: Colors.white,
                backgroundColor: colorScheme.primary,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // الأزرار في الجهة اليمنى مثل زر الوضع الليلي
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // مساحة فارغة للتوازن (مثل الشاشة الرئيسية)
                          const Spacer(),

                          // الأزرار في الجهة اليمنى
                          Row(
                            children: [
                              // زر سجل العمليات (يظهر دائماً)
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.history,
                                    color: Colors.white,
                                  ),
                                  tooltip: 'سجل العمليات',
                                  onPressed: () {
                                    _handleMenuSelection('transactions');
                                  },
                                ),
                              ),

                              // زر التعديل (يظهر حسب الشروط)
                              if (_canEdit()) ...[
                                const SizedBox(width: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: Colors.white,
                                    ),
                                    tooltip: 'تعديل',
                                    onPressed: () {
                                      _handleMenuSelection('edit');
                                    },
                                  ),
                                ),
                              ],

                              // زر الحذف (يظهر حسب الشروط)
                              if (_canDelete()) ...[
                                const SizedBox(width: 8),
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.red.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    tooltip: 'حذف',
                                    onPressed: () {
                                      _handleMenuSelection('delete');
                                    },
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // بطاقة معلومات المشترك الرئيسية
                      _buildMainCard(colorScheme, isDark, statusColor),
                      const SizedBox(height: 16),

                      // بطاقة التفاصيل
                      _buildDetailsCard(colorScheme, isDark),
                      const SizedBox(height: 16),

                      // بطاقة الإجراءات
                      _buildActionsCard(colorScheme, isDark),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// إرسال رسالة واتساب عند تسديد مبلغ
  Future<void> _sendWhatsAppPaymentMessage(
    double paidAmount,
    double remainingDebt,
    String note,
  ) async {
    try {
      // فحص إعدادات إرسال واتساب من قاعدة البيانات
      final db = await DBHelper.instance.database;
      final result = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: ['notifyPayment'],
        limit: 1,
      );

      bool shouldSendWhatsApp = true; // افتراضي
      if (result.isNotEmpty) {
        final value = result.first['value'];
        shouldSendWhatsApp = value == '1' || value == 1 || value == true;
      }

      if (!shouldSendWhatsApp) {
        debugPrint('[WHATSAPP] إرسال واتساب عند التسديد غير مفعل في الإعدادات');
        return;
      }

      // التحقق من وجود رقم هاتف
      if (_subscriber.phone.trim().isEmpty) {
        debugPrint('[WHATSAPP] لا يوجد رقم هاتف للمشترك');
        return;
      }

      // تنسيق رقم الهاتف
      String phone = _subscriber.phone.trim();
      if (phone.startsWith('0')) {
        phone = '964${phone.substring(1)}';
      }
      phone = phone.replaceAll(RegExp(r'[^0-9]'), '');

      // جلب رسالة التسديد من قاعدة البيانات
      final payMsg = await DBHelper.instance.getMessage(
        'pay_msg',
        'تم استلام دفعتك بقيمة {المبلغ_المسدد}. المتبقي عليك: {الدين}. شكراً لك {الاسم}.',
      );

      // استبدال المتغيرات في الرسالة
      String msg = payMsg
          .replaceAll('{الاسم}', _subscriber.name)
          .replaceAll('{رقم_الهاتف}', phone)
          .replaceAll('{نوع_الاشتراك}', _subscriber.subscriptionType)
          .replaceAll(
            '{سعر_الاشتراك}',
            _subscriber.subscriptionPrice.toString(),
          )
          .replaceAll(
            '{تاريخ_البدء}',
            _subscriber.startDate.toString().split(' ').first,
          )
          .replaceAll(
            '{تاريخ_الانتهاء}',
            _subscriber.endDate.toString().split(' ').first,
          )
          .replaceAll('{الدين}', remainingDebt.toString())
          .replaceAll('{المبلغ_المسدد}', paidAmount.toString());

      debugPrint('[WHATSAPP] محاولة إرسال رسالة تسديد للرقم: $phone');

      // محاولة فتح واتساب بالطريقة الصحيحة
      try {
        // المحاولة الأولى: واتساب العادي عبر الرابط المباشر
        final whatsappUri = Uri.parse(
          'https://wa.me/$phone?text=${Uri.encodeComponent(msg)}',
        );
        final launched = await launchUrl(
          whatsappUri,
          mode: LaunchMode.externalApplication,
        );

        if (launched) {
          debugPrint('[WHATSAPP] تم فتح واتساب بنجاح عبر wa.me');
        } else {
          debugPrint('[WHATSAPP] فشل فتح واتساب - launchUrl returned false');
        }
      } catch (e) {
        debugPrint('[WHATSAPP] فشل فتح واتساب عبر wa.me: $e');

        try {
          // المحاولة الثانية: البروتوكول المباشر
          final directUri = Uri.parse(
            'whatsapp://send?phone=$phone&text=${Uri.encodeComponent(msg)}',
          );
          final launched = await launchUrl(
            directUri,
            mode: LaunchMode.externalApplication,
          );

          if (launched) {
            debugPrint('[WHATSAPP] تم فتح واتساب بنجاح عبر البروتوكول المباشر');
          }
        } catch (e2) {
          debugPrint('[WHATSAPP] فشل فتح واتساب عبر البروتوكول المباشر: $e2');
        }
      }
    } catch (e) {
      debugPrint('[WHATSAPP] خطأ عام في إرسال رسالة التسديد: $e');
    }
  }

  /// بناء صف تفاصيل رقم الهاتف مع إمكانية الاتصال
  Widget _buildPhoneDetailRow(
    String label,
    String phone,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => _makePhoneCall(phone),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.phone, size: 16, color: colorScheme.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        phone,
                        style: TextStyle(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف تفاصيل عنوان IP مع إمكانية إضافة IP ثانوي
  Widget _buildClickableIPDetailRow(
    String label,
    String ip,
    ColorScheme colorScheme,
  ) {
    final hasSecondaryIP = _subscriber.secondaryIP?.isNotEmpty == true;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الصف الأساسي - مواقع ثابتة
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان IP مع زر الإضافة الصغير
              SizedBox(
                width: 120,
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        label,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ),
                    // زر إضافة صغير بجانب النص
                    if (!hasSecondaryIP)
                      Container(
                        margin: const EdgeInsets.only(left: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: InkWell(
                          onTap: () => _showAddSecondaryIPDialog(),
                          borderRadius: BorderRadius.circular(4),
                          child: Padding(
                            padding: const EdgeInsets.all(2),
                            child: Icon(
                              Icons.add,
                              size: 16,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              // حقل IP الأساسي - موقع ثابت
              Expanded(
                child: GestureDetector(
                  onTap: ip != 'غير محدد' ? () => _openIPInBrowser(ip) : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colorScheme.primary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.public,
                          size: 16,
                          color: ip != 'غير محدد'
                              ? colorScheme.primary
                              : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            ip,
                            style: TextStyle(
                              color: ip != 'غير محدد'
                                  ? colorScheme.primary
                                  : Colors.grey,
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        if (ip != 'غير محدد')
                          Icon(
                            Icons.touch_app,
                            size: 16,
                            color: colorScheme.primary.withValues(alpha: 0.7),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // الصف الثانوي - يظهر عند الإضافة
          if (hasSecondaryIP) ...[
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // نص "IP ثانوي" مع زر التعديل
                SizedBox(
                  width: 120,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'IP ثانوي',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.withValues(alpha: 0.8),
                            fontSize: 14,
                          ),
                        ),
                      ),
                      // زر تعديل صغير
                      Container(
                        margin: const EdgeInsets.only(left: 4),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Colors.orange.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: InkWell(
                          onTap: () => _showEditSecondaryIPDialog(
                            _subscriber.secondaryIP!,
                          ),
                          borderRadius: BorderRadius.circular(4),
                          child: Padding(
                            padding: const EdgeInsets.all(2),
                            child: Icon(
                              Icons.edit,
                              size: 16,
                              color: Colors.orange,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // حقل IP الثانوي
                Expanded(
                  child: GestureDetector(
                    onTap: () => _openIPInBrowser(_subscriber.secondaryIP!),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.orange.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.public,
                            size: 16,
                            color: Colors.orange,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _subscriber.secondaryIP!,
                              style: const TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Icon(
                            Icons.touch_app,
                            size: 16,
                            color: Colors.orange.withValues(alpha: 0.7),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// عرض حوار إضافة IP ثانوي
  Future<void> _showAddSecondaryIPDialog() async {
    final TextEditingController ipController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final colorScheme = Theme.of(context).colorScheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.add, color: colorScheme.primary),
              const SizedBox(width: 8),
              const Text('إضافة IP ثانوي'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('أدخل عنوان IP الثانوي:'),
              const SizedBox(height: 12),
              TextField(
                controller: ipController,
                decoration: InputDecoration(
                  hintText: 'مثال: *************',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.language, color: colorScheme.primary),
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newIP = ipController.text.trim();
                if (newIP.isNotEmpty) {
                  Navigator.of(context).pop();
                  await _saveSecondaryIP(newIP);
                }
              },
              child: const Text('إضافة'),
            ),
          ],
        );
      },
    );
  }

  /// عرض حوار تعديل IP ثانوي
  Future<void> _showEditSecondaryIPDialog(String currentIP) async {
    final TextEditingController ipController = TextEditingController(
      text: currentIP,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final colorScheme = Theme.of(context).colorScheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.edit, color: colorScheme.primary),
              const SizedBox(width: 8),
              const Text('تعديل IP الثانوي'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('تعديل عنوان IP الثانوي:'),
              const SizedBox(height: 12),
              TextField(
                controller: ipController,
                decoration: InputDecoration(
                  hintText: 'مثال: *************',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: Icon(Icons.language, color: colorScheme.primary),
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _removeSecondaryIP();
              },
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () async {
                final newIP = ipController.text.trim();
                if (newIP.isNotEmpty) {
                  Navigator.of(context).pop();
                  await _saveSecondaryIP(newIP);
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  /// حفظ IP ثانوي
  Future<void> _saveSecondaryIP(String newIP) async {
    try {
      final db = await DBHelper.instance.database;

      // التأكد من وجود العمود أولاً
      final columns = await db.rawQuery("PRAGMA table_info(subscribers)");
      final hasSecondaryIP = columns.any((c) => c['name'] == 'secondary_ip');

      if (!hasSecondaryIP) {
        // إضافة العمود إذا لم يكن موجوداً
        await db.execute(
          'ALTER TABLE subscribers ADD COLUMN secondary_ip TEXT;',
        );
        debugPrint('[DB] تم إضافة عمود secondary_ip إلى جدول subscribers');
      }

      // تحديث IP الثانوي
      final result = await db.update(
        'subscribers',
        {'secondary_ip': newIP},
        where: 'id = ?',
        whereArgs: [_subscriber.id],
      );

      debugPrint(
        '[DB] تم تحديث IP الثانوي: $newIP، عدد الصفوف المحدثة: $result',
      );

      if (mounted) {
        setState(() {
          _subscriber = _subscriber.copyWith(secondaryIP: newIP);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ IP الثانوي: $newIP'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('[DB] خطأ في حفظ IP الثانوي: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ IP الثانوي: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حذف IP ثانوي
  Future<void> _removeSecondaryIP() async {
    try {
      final db = await DBHelper.instance.database;

      final result = await db.update(
        'subscribers',
        {'secondary_ip': null},
        where: 'id = ?',
        whereArgs: [_subscriber.id],
      );

      debugPrint('[DB] تم حذف IP الثانوي، عدد الصفوف المحدثة: $result');

      if (mounted) {
        setState(() {
          _subscriber = _subscriber.copyWith(secondaryIP: null);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف IP الثانوي'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      debugPrint('[DB] خطأ في حذف IP الثانوي: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف IP الثانوي: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إجراء مكالمة هاتفية
  Future<void> _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف
      String cleanPhone = phoneNumber.replaceAll(RegExp(r'[^0-9+]'), '');

      if (cleanPhone.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('رقم الهاتف غير صحيح'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final phoneUri = Uri.parse('tel:$cleanPhone');
      final launched = await launchUrl(phoneUri);

      if (!launched) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تعذر فتح تطبيق الهاتف'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else {
        debugPrint('[PHONE_CALL] تم فتح تطبيق الهاتف للرقم: $cleanPhone');
      }
    } catch (e) {
      debugPrint('[PHONE_CALL] خطأ في إجراء المكالمة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إجراء المكالمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
