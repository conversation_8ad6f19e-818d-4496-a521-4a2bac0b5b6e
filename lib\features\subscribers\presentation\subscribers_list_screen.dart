import '../../../auto_notifications_screen.dart' as auto_notify;
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:url_launcher/url_launcher.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import 'add_subscriber_screen.dart';
import 'subscriber_details_screen.dart';
import 'subscription_prices_screen.dart';
import 'renew_subscription_bottom_sheet.dart';
import '../../../db_helper.dart';
import 'package:sqflite/sqflite.dart';
import '../../../main.dart';
import '../../../core/utils/notification_service.dart';
import '../../sources_screen.dart';
import '../../supabase_account_screen.dart';

// تعريف كلاس خيارات الترتيب في الأعلى قبل الاستخدام
class _SortOption {
  final String label;
  final IconData icon;
  final String key;
  const _SortOption(this.label, this.icon, this.key);
}

class SubscribersListScreen extends StatefulWidget {
  final SubscribersRepository repository;
  final Map<String, dynamic>? filter;
  final bool isDarkMode;
  final VoidCallback onToggleTheme;
  final void Function(BuildContext context) showThemeStatus;
  final bool isTrialExpired;
  const SubscribersListScreen({
    super.key,
    required this.repository,
    this.filter,
    required this.isDarkMode,
    required this.onToggleTheme,
    required this.showThemeStatus,
    this.isTrialExpired = false,
  });

  @override
  State<SubscribersListScreen> createState() => _SubscribersListScreenState();
}

class _SubscribersListScreenState extends State<SubscribersListScreen>
    with RouteAware {
  // ...existing fields...

  @override
  void didPopNext() {
    // عند العودة من شاشة أخرى، أعد تحميل حالة الإشعارات من قاعدة البيانات
    // استخدام Future.microtask لتجنب مشاكل async في didPopNext
    Future.microtask(() async {
      if (!mounted) return;

      try {
        await _loadNotificationStatesFromDb();
        if (!mounted) return;

        await _fetchSubscribers();
      } catch (e) {
        debugPrint('خطأ في didPopNext: $e');
        // في حالة الخطأ، لا نفعل شيء لتجنب توقف التطبيق
      }
    });
  }

  List<Subscriber> subscribers = [];
  bool isLoading = true;
  bool _showSearch = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool showNano = true; // متغير تحكم عرض إشارة النانو
  bool _isRouteSubscribed = false; // متغير لتتبع الاشتراك
  bool _showExpireSoonIcon =
      false; // متغير للتحكم في عرض أيقونة التنبيه قرب الانتهاء
  bool _showDebtReminderIcon = false; // متغير للتحكم في عرض أيقونة تذكير الديون

  // خريطة لتتبع آخر وقت إرسال فعلي من قاعدة البيانات
  final Map<String, DateTime> _expireSoonSent = {};
  final Map<String, DateTime> _debtReminderSent = {};

  Map<String, Map<String, dynamic>> _subscriptionsMap = {};

  // خيارات الترتيب (مطلوب فقط: الاسم، الأيام المتبقية، المبلغ المستحق)
  final List<_SortOption> _sortOptions = [
    _SortOption('الاسم (أ-ي)', Icons.sort_by_alpha, 'name'),
    _SortOption('الأيام المتبقية', Icons.timer, 'daysLeft'),
    _SortOption('المبلغ المستحق', Icons.attach_money, 'totalDebt'),
  ];
  String _selectedSortKey = 'daysLeft'; // الافتراضي: الأيام المتبقية

  @override
  void initState() {
    super.initState();
    _initAll();
    _isRouteSubscribed = false;
    syncCompletedNotifier.addListener(_onSyncCompleted);
  }

  Future<void> _initAll() async {
    await _initializeDatabase();
    await _loadNotificationStatesFromDb();
    await _fetchSubscriptionsAndSubscribers();
    await _loadDebtReminderOption();
    await _loadExpireSoonOption();

    // فحص الإشعارات المفقودة عند بدء التطبيق
    await NotificationService().checkMissedNotifications();

    // ملاحظة: تم إزالة _performDailySync() من هنا لأنها تتم في main.dart
    // المزامنة اليومية للـ Firebase تتم مرة واحدة يومياً وليس في كل فتح للشاشة

    await Future.delayed(
      Duration.zero,
      () => _fetchSubscribers(autoNotify: true),
    );
  }

  // دالة لإعداد قاعدة البيانات مرة واحدة فقط
  Future<void> _initializeDatabase() async {
    try {
      final db = await DBHelper.instance.database;

      // فحص إذا كانت الجداول تحتاج إعادة إنشاء
      bool needsRecreation = false;

      try {
        // محاولة فحص هيكل الجدول
        final result = await db.rawQuery(
          "PRAGMA table_info(me_debt_notifications)",
        );
        final hasSubscriberId = result.any(
          (column) => column['name'] == 'subscriber_id',
        );
        final hasDebtAmount = result.any(
          (column) => column['name'] == 'debt_amount',
        );

        if (!hasSubscriberId || !hasDebtAmount) {
          needsRecreation = true;
        }
      } catch (e) {
        // الجدول غير موجود
        needsRecreation = true;
      }

      if (needsRecreation) {
        debugPrint('تحديث هيكل الجداول مع الحفاظ على البيانات');

        // نسخ البيانات الموجودة قبل التحديث
        List<Map<String, dynamic>> existingDebtData = [];
        List<Map<String, dynamic>> existingNearEndData = [];

        try {
          existingDebtData = await db.query('me_debt_notifications');
        } catch (e) {
          debugPrint('لا توجد بيانات ديون سابقة: $e');
        }

        try {
          existingNearEndData = await db.query('me_near_end_notifications');
        } catch (e) {
          debugPrint('لا توجد بيانات قرب انتهاء سابقة: $e');
        }

        // حذف الجداول القديمة
        await db.execute('DROP TABLE IF EXISTS me_debt_notifications');
        await db.execute('DROP TABLE IF EXISTS me_near_end_notifications');

        // إنشاء الجداول الجديدة
        await db.execute('''
          CREATE TABLE me_debt_notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscriber_id TEXT,
            name TEXT NOT NULL,
            debt_amount REAL,
            sent_at TEXT NOT NULL
          )
        ''');

        await db.execute('''
          CREATE TABLE me_near_end_notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subscriber_id TEXT,
            name TEXT NOT NULL,
            sent_at TEXT NOT NULL
          )
        ''');

        // استعادة البيانات المحفوظة
        for (final row in existingDebtData) {
          try {
            await db.insert('me_debt_notifications', {
              'subscriber_id': row['subscriber_id'],
              'name': row['name'],
              'debt_amount': row['debt_amount'],
              'sent_at': row['sent_at'],
            });
          } catch (e) {
            debugPrint('خطأ في استعادة بيانات الديون: $e');
          }
        }

        for (final row in existingNearEndData) {
          try {
            await db.insert('me_near_end_notifications', {
              'subscriber_id': row['subscriber_id'],
              'name': row['name'],
              'sent_at': row['sent_at'],
            });
          } catch (e) {
            debugPrint('خطأ في استعادة بيانات قرب الانتهاء: $e');
          }
        }

        debugPrint('تم تحديث الجداول مع الحفاظ على البيانات');
      } else {
        debugPrint('الجداول موجودة بالهيكل الصحيح');
      }
    } catch (e) {
      debugPrint('خطأ في إعداد قاعدة البيانات: $e');
    }
  }

  Future<void> _loadNotificationStatesFromDb() async {
    try {
      // فحص حالة mounted قبل بدء العملية
      if (!mounted) return;

      final db = await DBHelper.instance.database;

      // إنشاء الجداول بالهيكل الصحيح إذا لم تكن موجودة
      await db.execute('''
        CREATE TABLE IF NOT EXISTS me_debt_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          subscriber_id TEXT,
          name TEXT NOT NULL,
          debt_amount REAL,
          sent_at TEXT NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS me_near_end_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          subscriber_id TEXT,
          name TEXT NOT NULL,
          sent_at TEXT NOT NULL
        )
      ''');

      // إنشاء جدول إشعارات انتهاء الاشتراك
      await db.execute('''
        CREATE TABLE IF NOT EXISTS me_end_notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          end_date TEXT NOT NULL,
          sent_at TEXT NOT NULL
        )
      ''');

      debugPrint('تم التأكد من وجود الجداول بالهيكل الصحيح');

      // تنظيف البيانات القديمة (أكثر من 7 أيام)
      await _cleanOldNotifications();

      // ديون
      final debtRows = await db.query('me_debt_notifications');
      _debtReminderSent.clear();
      for (final row in debtRows) {
        final subscriberId = row['subscriber_id']?.toString();
        final name = row['name']?.toString() ?? '';
        final sentAt = row['sent_at']?.toString();

        if (sentAt != null) {
          final dt = DateTime.tryParse(sentAt);
          if (dt != null) {
            // استخدم نفس منطق المفتاح الموحد تماماً
            final key = subscriberId ?? 'name_$name';
            _debtReminderSent[key] = dt;
            debugPrint(
              'تحميل حالة إرسال الدين من قاعدة البيانات - المفتاح: $key, التاريخ: $dt',
            );
          }
        }
      }

      // قرب انتهاء
      final nearRows = await db.query('me_near_end_notifications');
      _expireSoonSent.clear();
      for (final row in nearRows) {
        final subscriberId = row['subscriber_id']?.toString();
        final name = row['name']?.toString() ?? '';
        final sentAt = row['sent_at']?.toString();

        if (sentAt != null) {
          final dt = DateTime.tryParse(sentAt);
          if (dt != null) {
            // استخدم نفس منطق المفتاح الموحد
            final key = subscriberId ?? 'name_$name';
            _expireSoonSent[key] = dt;
          }
        }
      }
      if (mounted) setState(() {});
    } catch (e) {
      debugPrint('خطأ في تحميل حالات الإشعارات: $e');
    }
  }

  void _onSyncCompleted() {
    // عند أي تغيير في المزامنة، جلب المشتركين مباشرة
    if (mounted) {
      _fetchSubscribers();
    }
  }

  Future<void> _fetchSubscriptionsAndSubscribers() async {
    final subsList = await DBHelper.instance.getAllSubscriptions();
    if (mounted) {
      setState(() {
        _subscriptionsMap = {for (var s in subsList) s['id']: s};
      });
    }
    await _fetchSubscribers();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // تنفيذ العمليات async بشكل آمن
    Future.microtask(() async {
      if (!mounted) return;

      try {
        await _loadShowNano();
        if (!mounted) return;

        await _loadNotificationStatesFromDb();
      } catch (e) {
        debugPrint('خطأ في didChangeDependencies: $e');
      }
    });

    // تسجيل الشاشة لدى RouteObserver مرة واحدة فقط
    if (!_isRouteSubscribed && ModalRoute.of(context) != null) {
      routeObserver.subscribe(this, ModalRoute.of(context)!);
      _isRouteSubscribed = true;
    }
  }

  // ملاحظة: تم نقل المزامنة اليومية إلى main.dart لتتم مرة واحدة يومياً
  // بدلاً من كل فتح لشاشة المشتركين

  @override
  void dispose() {
    // إزالة مستمع المزامنة
    syncCompletedNotifier.removeListener(_onSyncCompleted);
    // إلغاء التسجيل عند التخلص من الشاشة إذا تم الاشتراك
    if (_isRouteSubscribed) {
      routeObserver.unsubscribe(this);
      _isRouteSubscribed = false;
    }
    super.dispose();
  }

  Future<void> _loadShowNano() async {
    // جلب قيمة showNano من الإعدادات (قاعدة البيانات)
    final db = await DBHelper.instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['showNano'],
      limit: 1,
    );
    final newValue = result.isEmpty ? true : (result.first['value'] == '1');
    if (showNano != newValue && mounted) {
      setState(() {
        showNano = newValue;
      });
    }
  }

  Future<void> _fetchSubscribers({bool autoNotify = false}) async {
    // جلب جميع المشتركين (يدوي ومن السيرفر)
    final data = await widget.repository.getAllSubscribers();
    List<Subscriber> filtered = data;
    final filter = widget.filter;
    if (filter != null && filter.isNotEmpty) {
      // فلترة دقيقة حسب الفلاتر القادمة من الشاشة الرئيسية فقط
      if (filter.containsKey('active') && filter['active'] == true) {
        // فلتر النشطين: المشتركين الذين لم تنته اشتراكاتهم بعد
        filtered = filtered
            .where((s) => s.endDate.isAfter(DateTime.now()))
            .toList();
      }
      if (filter.containsKey('onlineStatus') && filter['onlineStatus'] == 1) {
        filtered = filtered.where((s) => s.onlineStatus == 1).toList();
      }
      if (filter.containsKey('notExpired') && filter['notExpired'] == true) {
        filtered = filtered
            .where((s) => s.endDate.isAfter(DateTime.now()))
            .toList();
      }
      if (filter.containsKey('expired') && filter['expired'] == true) {
        filtered = filtered
            .where(
              (s) =>
                  s.endDate.isBefore(DateTime.now()) ||
                  s.endDate.isAtSameMomentAs(DateTime.now()),
            )
            .toList();
      }
      if (filter.containsKey('totalDebt') && filter['totalDebt'] == '>0') {
        filtered = filtered.where((s) => s.totalDebt > 0).toList();
      }
      if (filter.containsKey('contract')) {
        filtered = filtered
            .where((s) => s.contract == filter['contract'])
            .toList();
      }
      // إذا كان الفلتر فارغ أو لا يحتوي على أي مفتاح معروف، لا تطبق أي تصفية إضافية
    }
    if (autoNotify) {
      final now = DateTime.now();
      final db = await DBHelper.instance.database;
      // إشعار قرب الانتهاء
      for (final sub in filtered) {
        if (sub.endDate.isBefore(DateTime.now())) continue;
        // تحقق من قاعدة بيانات الإشعارات
        final nearRows = await db.query(
          'me_near_end_notifications',
          where: 'name = ?',
          whereArgs: [sub.name],
          limit: 1,
        );
        DateTime? lastSent;
        if (nearRows.isNotEmpty && nearRows.first['sent_at'] != null) {
          lastSent = DateTime.tryParse(nearRows.first['sent_at'].toString());
        }
        final sent24h =
            lastSent != null && now.difference(lastSent).inHours < 24;
        if (!sent24h && sub.endDate.difference(now).inHours < 48) {
          await NotificationService().showEndNotification(
            sub.name,
            sub.endDate,
          );
          await db.insert(
            'me_near_end_notifications',
            {'name': sub.name, 'sent_at': now.toIso8601String()},
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      }
      // إشعار انتهاء الاشتراكات فقط
      for (final sub in filtered) {
        // التحقق من انتهاء الاشتراك
        if (sub.endDate.isBefore(now) || sub.endDate.isAtSameMomentAs(now)) {
          // فحص إذا تم إرسال إشعار انتهاء لهذا المشترك اليوم
          final endRows = await db.query(
            'me_end_notifications',
            where: 'name = ? AND DATE(sent_at) = DATE(?)',
            whereArgs: [sub.name, now.toIso8601String()],
            limit: 1,
          );

          if (endRows.isEmpty) {
            // إرسال إشعار انتهاء الاشتراك
            await NotificationService().showEndNotification(
              sub.name,
              sub.endDate,
            );

            // تسجيل الإشعار في قاعدة البيانات
            await db.insert('me_end_notifications', {
              'name': sub.name,
              'end_date': sub.endDate.toIso8601String(),
              'sent_at': now.toIso8601String(),
            }, conflictAlgorithm: ConflictAlgorithm.replace);

            debugPrint('تم إرسال إشعار انتهاء اشتراك للمشترك: ${sub.name}');
          }
        }
      }
    }

    if (mounted) {
      setState(() {
        subscribers = filtered;
        isLoading = false;
      });
    }
  }

  Future<bool> _hasAnySubscription() async {
    // استدعاء دالة من DBHelper أو repository للتحقق من وجود باقة واحدة على الأقل
    final list = await DBHelper.instance.getAllSubscriptions();
    return list.isNotEmpty;
  }

  Future<void> _loadExpireSoonOption() async {
    // جلب حالة خيار قرب الانتهاء من قاعدة البيانات
    final db = await DBHelper.instance.database;
    final row = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['notifyNearEnd'], // توحيد التسمية مع شاشة الإعدادات
      limit: 1,
    );
    if (mounted) {
      setState(() {
        _showExpireSoonIcon =
            row.isNotEmpty &&
            (row.first['value'] == '1' ||
                row.first['value'] == true ||
                row.first['value'] == 'true');
      });
    }
  }

  Future<void> _loadDebtReminderOption() async {
    // جلب حالة خيار تذكير الديون من قاعدة البيانات
    final db = await DBHelper.instance.database;
    final row = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['notifyDebt'],
      limit: 1,
    );
    if (mounted) {
      setState(() {
        _showDebtReminderIcon =
            row.isNotEmpty &&
            (row.first['value'] == '1' ||
                row.first['value'] == true ||
                row.first['value'] == 'true');
      });
    }
  }

  bool _isExpireSoonSentToday(Subscriber sub) {
    final now = DateTime.now();
    final key = _getSubscriberKey(sub);
    final sent = _expireSoonSent[key];

    if (sent != null) {
      // تحقق من نفس اليوم وليس 24 ساعة
      return sent.year == now.year &&
          sent.month == now.month &&
          sent.day == now.day;
    }
    return false;
  }

  // دالة مساعدة لتوحيد مفتاح التتبع
  String _getSubscriberKey(Subscriber sub) {
    return sub.id?.toString() ?? 'name_${sub.name}';
  }

  // دالة تنظيف البيانات القديمة
  Future<void> _cleanOldNotifications() async {
    try {
      final db = await DBHelper.instance.database;
      final weekAgo = DateTime.now().subtract(const Duration(days: 7));

      // حذف إشعارات الديون القديمة
      await db.delete(
        'me_debt_notifications',
        where: 'sent_at < ?',
        whereArgs: [weekAgo.toIso8601String()],
      );

      // حذف إشعارات قرب الانتهاء القديمة
      await db.delete(
        'me_near_end_notifications',
        where: 'sent_at < ?',
        whereArgs: [weekAgo.toIso8601String()],
      );
    } catch (e) {
      debugPrint('خطأ في تنظيف الإشعارات القديمة: $e');
    }
  }

  bool _isDebtReminderSentToday(Subscriber sub) {
    final now = DateTime.now();
    final key = _getSubscriberKey(sub);
    final sent = _debtReminderSent[key];

    debugPrint(
      'تحقق من إرسال تذكير الدين - المشترك: ${sub.name}, المفتاح: $key',
    );
    debugPrint('تاريخ الإرسال المحفوظ: $sent');
    debugPrint('التاريخ الحالي: $now');

    if (sent != null) {
      // تحقق من نفس اليوم وليس 24 ساعة
      final isSameDay =
          sent.year == now.year &&
          sent.month == now.month &&
          sent.day == now.day;
      debugPrint('هل تم الإرسال اليوم؟ $isSameDay');
      return isSameDay;
    }
    debugPrint('لم يتم العثور على تاريخ إرسال - لم يتم الإرسال اليوم');
    return false;
  }

  Future<void> _sendExpireSoonMsg(Subscriber sub) async {
    final now = DateTime.now();
    if (_isExpireSoonSentToday(sub)) return;
    try {
      final expireMsg = await DBHelper.instance.getMessage(
        'expire_soon_msg',
        'عزيزي {الاسم}، اشتراكك سينتهي قريباً بتاريخ {تاريخ_الانتهاء}. يرجى التجديد لضمان استمرار الخدمة.',
      );
      String phone = sub.phone.trim();
      if (phone.startsWith('0')) {
        phone = '964' + phone.substring(1);
      }
      phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
      String? startDateStr;
      if (expireMsg.contains('{تاريخ_البدء}')) {
        startDateStr = await auto_notify.formatDateForMessage(sub.startDate);
      }
      String msg = await auto_notify.fillMessageVars(
        expireMsg,
        name: sub.name,
        phone: phone,
        type: sub.subscriptionType,
        price: sub.subscriptionPrice.toString(),
        startDate: startDateStr,
        endDate: sub.endDate,
        debt: sub.totalDebt.toString(),
      );
      // محاولة فتح واتساب الأعمال أولاً، ثم واتساب العادي
      final message = Uri.encodeComponent(msg);
      debugPrint('محاولة فتح واتساب للرقم: $phone');

      // محاولة فتح واتساب بالطريقة الصحيحة
      bool whatsappOpened = false;

      try {
        // المحاولة الأولى: واتساب العادي عبر الرابط المباشر
        final whatsappUri = Uri.parse('https://wa.me/$phone?text=$message');
        final launched = await launchUrl(
          whatsappUri,
          mode: LaunchMode.externalApplication,
        );

        if (launched) {
          whatsappOpened = true;
          debugPrint('تم فتح واتساب بنجاح عبر wa.me');
        }
      } catch (e) {
        debugPrint('فشل فتح واتساب عبر wa.me: $e');

        try {
          // المحاولة الثانية: البروتوكول المباشر
          final directUri = Uri.parse(
            'whatsapp://send?phone=$phone&text=$message',
          );
          final launched = await launchUrl(
            directUri,
            mode: LaunchMode.externalApplication,
          );

          if (launched) {
            whatsappOpened = true;
            debugPrint('تم فتح واتساب بنجاح عبر البروتوكول المباشر');
          }
        } catch (e2) {
          debugPrint('فشل فتح واتساب عبر البروتوكول المباشر: $e2');
        }
      }

      // افترض النجاح دائماً وحدث قاعدة البيانات
      // لأن واتساب الأعمال قد يفتح حتى لو رمى launchUrl استثناء
      try {
        final db = await DBHelper.instance.database;
        await db.insert(
          'me_near_end_notifications',
          {
            'subscriber_id': sub.id,
            'name': sub.name,
            'sent_at': now.toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        if (mounted) {
          final key = _getSubscriberKey(sub);
          _expireSoonSent[key] = now;
          setState(() {});

          // إظهار رسالة نجاح دائماً
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم إرسال تنبيه قرب الانتهاء إلى ${sub.name}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } catch (dbError) {
        // خطأ في قاعدة البيانات فقط
        debugPrint('خطأ في حفظ البيانات: $dbError');
      }
    } catch (e) {
      // تجاهل الخطأ أو أظهر رسالة إذا رغبت
    }
  }

  Future<void> _sendDebtReminderMsg(Subscriber sub) async {
    final now = DateTime.now();
    debugPrint('محاولة إرسال تذكير دين للمشترك: ${sub.name}');

    if (_isDebtReminderSentToday(sub)) {
      debugPrint('تم إرسال تذكير الدين اليوم بالفعل - إيقاف الإرسال');
      return;
    }

    debugPrint('لم يتم إرسال تذكير اليوم - المتابعة مع الإرسال');
    try {
      // استخدم الرسالة القابلة للتحرير من شاشة الإشعارات التلقائية
      final debtMsg = await DBHelper.instance.getMessage(
        'rem_msg',
        'عزيزي {الاسم}، يرجى تسديد مبلغ الديون  {الدين}.',
      );
      String phone = sub.phone.trim();
      if (phone.startsWith('0')) {
        phone = '964' + phone.substring(1);
      }
      phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
      String msg = await auto_notify.fillMessageVars(
        debtMsg,
        name: sub.name,
        phone: phone,
        type: sub.subscriptionType,
        price: sub.subscriptionPrice.toString(),
        endDate: sub.endDate,
        debt: sub.totalDebt.toString(),
      );
      // محاولة فتح واتساب الأعمال أولاً، ثم واتساب العادي
      final message = Uri.encodeComponent(msg);
      debugPrint('محاولة فتح واتساب للرقم: $phone');

      // محاولة فتح واتساب بالطريقة الصحيحة
      try {
        // المحاولة الأولى: واتساب العادي عبر الرابط المباشر
        final whatsappUri = Uri.parse('https://wa.me/$phone?text=$message');
        final launched = await launchUrl(
          whatsappUri,
          mode: LaunchMode.externalApplication,
        );

        if (launched) {
          debugPrint('تم فتح واتساب بنجاح عبر wa.me');
        }
      } catch (e) {
        debugPrint('فشل فتح واتساب عبر wa.me: $e');

        try {
          // المحاولة الثانية: البروتوكول المباشر
          final directUri = Uri.parse(
            'whatsapp://send?phone=$phone&text=$message',
          );
          final launched = await launchUrl(
            directUri,
            mode: LaunchMode.externalApplication,
          );

          if (launched) {
            debugPrint('تم فتح واتساب بنجاح عبر البروتوكول المباشر');
          }
        } catch (e2) {
          debugPrint('فشل فتح واتساب عبر البروتوكول المباشر: $e2');
        }
      }

      // افترض النجاح دائماً وحدث قاعدة البيانات
      // لأن واتساب الأعمال قد يفتح حتى لو رمى launchUrl استثناء
      try {
        final db = await DBHelper.instance.database;

        // حفظ في قاعدة البيانات أولاً
        await db.insert('me_debt_notifications', {
          'subscriber_id': sub.id,
          'name': sub.name,
          'debt_amount': sub.totalDebt,
          'sent_at': now.toIso8601String(),
        }, conflictAlgorithm: ConflictAlgorithm.replace);

        debugPrint('تم حفظ البيانات في قاعدة البيانات بنجاح');

        if (mounted) {
          final key = _getSubscriberKey(sub);
          debugPrint('تحديث حالة الإرسال - المفتاح: $key, الوقت: $now');

          // تحديث الذاكرة
          _debtReminderSent[key] = now;
          debugPrint('تم حفظ الحالة في الذاكرة: ${_debtReminderSent[key]}');

          // إعادة تحميل حالات الإشعارات من قاعدة البيانات للتأكد
          await _loadNotificationStatesFromDb();
          debugPrint('تم إعادة تحميل حالات الإشعارات من قاعدة البيانات');

          if (mounted) {
            setState(() {});
            debugPrint('تم استدعاء setState لتحديث الواجهة');
          }

          // إظهار رسالة نجاح دائماً مع فحص mounted
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('تم إرسال تذكير الدين إلى ${sub.name}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      } catch (dbError) {
        // خطأ في قاعدة البيانات فقط
        debugPrint('خطأ في حفظ البيانات: $dbError');

        // حتى لو فشل الحفظ في قاعدة البيانات، احفظ في الذاكرة
        if (mounted) {
          final key = _getSubscriberKey(sub);
          _debtReminderSent[key] = now;
          setState(() {});
        }
      }
    } catch (e) {
      // تجاهل الخطأ أو أظهر رسالة إذا رغبت
    }
  }

  // دالة لإظهار رسالة عند الضغط على أيقونة تم إرسال تذكير لها بالفعل
  void _showAlreadySentMessage(String subscriberName) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إرسال تذكير للمشترك "$subscriberName" بالفعل اليوم.\nيتم إرسال التذكير مرة واحدة خلال اليوم.',
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  // دالة لإظهار رسالة عند الضغط على أيقونة تم إرسال تنبيه قرب الانتهاء لها بالفعل
  void _showAlreadySentExpireMessage(String subscriberName) {
    debugPrint(
      'استدعاء _showAlreadySentExpireMessage للمشترك: $subscriberName',
    );
    if (mounted) {
      debugPrint('إظهار رسالة تنبيه قرب الانتهاء للمشترك: $subscriberName');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم إرسال تنبيه قرب الانتهاء للمشترك "$subscriberName" بالفعل اليوم.\nيتم إرسال التنبيه مرة واحدة خلال اليوم.',
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } else {
      debugPrint('Widget غير mounted - لا يمكن إظهار الرسالة');
    }
  }

  // دوال الترتيب والمساعدة يجب أن تكون قبل build
  void _sortSubscribers(List<Subscriber> list) {
    switch (_selectedSortKey) {
      case 'name':
        list.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'daysLeft':
        list.sort(
          (a, b) => a.endDate
              .difference(DateTime.now())
              .compareTo(b.endDate.difference(DateTime.now())),
        );
        break;
      case 'totalDebt':
        list.sort((a, b) => b.totalDebt.compareTo(a.totalDebt));
        break;
      default:
        break;
    }
  }

  String _daysAndHours(Subscriber subscriber) {
    if (subscriber.isDeletedStatus) {
      return 'تم حذفه من السيرفر';
    }
    final remainingDuration = subscriber.endDate.difference(DateTime.now());
    final totalSeconds = remainingDuration.inSeconds;
    if (totalSeconds <= 0) {
      return 'منتهي';
    }
    final days = remainingDuration.inDays;
    if (days > 0) {
      return '$days يوم';
    } else {
      final hours = remainingDuration.inHours;
      if (hours > 0) {
        return '$hours ساعة';
      } else {
        final minutes = remainingDuration.inMinutes;
        return '$minutes دقيقة';
      }
    }
  }

  void _showFilterBottomSheet(
    BuildContext context,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'تصفية المشتركين',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: Icon(Icons.people, color: colorScheme.primary),
              title: const Text('جميع المشتركين'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  // إعادة تحميل جميع المشتركين
                  _fetchSubscribers();
                });
              },
            ),
            ListTile(
              leading: Icon(Icons.check_circle, color: Colors.green),
              title: const Text('المشتركين النشطين'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  subscribers = subscribers
                      .where((s) => s.endDate.isAfter(DateTime.now()))
                      .toList();
                });
              },
            ),
            ListTile(
              leading: Icon(Icons.cancel, color: Colors.red),
              title: const Text('المشتركين المنتهيين'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  subscribers = subscribers
                      .where((s) => s.endDate.isBefore(DateTime.now()))
                      .toList();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // أزرار الإجراءات في العمود الأيسر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // العمود الأيسر للأزرار
              Column(
                children: [
                  // زر البحث (الأول في الأعلى)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(
                        _showSearch ? Icons.close : Icons.search,
                        color: colorScheme.onPrimary,
                      ),
                      tooltip: _showSearch ? 'إغلاق البحث' : 'البحث',
                      onPressed: () {
                        setState(() {
                          _showSearch = !_showSearch;
                          if (!_showSearch) {
                            _searchQuery = '';
                          }
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 12),

                  // زر التصفية (الثاني)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: colorScheme.onPrimary,
                      ),
                      tooltip: 'ترتيب وتصفية',
                      onPressed: () {
                        _showSortOptions(context);
                      },
                    ),
                  ),
                  const SizedBox(height: 12),

                  // زر الإضافة (الثالث)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.person_add,
                        color: colorScheme.onPrimary,
                      ),
                      tooltip: 'إضافة مشترك',
                      onPressed: () async {
                        await _handleAddSubscriber();
                      },
                    ),
                  ),
                ],
              ),

              // مساحة فارغة للتوازن
              const SizedBox(width: 48),
            ],
          ),
          const SizedBox(height: 16),

          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(Icons.people, color: colorScheme.primary, size: 54),
            ),
          ),

          // العنوان والوصف
          Text(
            'المشتركين',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة وعرض جميع المشتركين',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.92),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء شريط البحث
  Widget _buildSearchBar(ColorScheme colorScheme, bool isDark) {
    if (!_showSearch) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Card(
          elevation: 0,
          color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'البحث في المشتركين (الاسم، الهاتف، اليوزر)...',
                prefixIcon: Icon(Icons.search, color: colorScheme.primary),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                filled: true,
                fillColor: colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ),
      ),
    );
  }

  // معالجة إضافة مشترك جديد
  Future<void> _handleAddSubscriber() async {
    try {
      // التحقق من وجود اشتراكات أولاً
      final hasSubscription = await _hasAnySubscription();
      if (!hasSubscription) {
        if (mounted) {
          final goToAdd = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('لا توجد باقات'),
              content: const Text(
                'يجب إضافة باقة اشتراك أولاً قبل إضافة المشتركين.\nهل تريد الذهاب لإضافة باقة؟',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('إضافة باقة'),
                ),
              ],
            ),
          );
          if (goToAdd == true) {
            if (mounted) {
              await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const SubscriptionPricesScreen(),
                ),
              );
            }
            return;
          }
        }
      }

      // الانتقال إلى شاشة إضافة المشترك
      if (mounted) {
        final added = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => AddSubscriberScreen(repository: widget.repository),
          ),
        );
        if (added == true) {
          _fetchSubscribers();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  // عرض خيارات الترتيب
  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'ترتيب بحسب',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            ..._sortOptions.map(
              (option) => ListTile(
                leading: Icon(
                  option.icon,
                  color: option.key == _selectedSortKey ? Colors.blue : null,
                ),
                title: Text(
                  option.label,
                  style: TextStyle(
                    color: option.key == _selectedSortKey ? Colors.blue : null,
                    fontWeight: option.key == _selectedSortKey
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
                onTap: () {
                  setState(() {
                    _selectedSortKey = option.key;
                  });
                  Navigator.pop(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // إعادة تحميل خيار التنبيه وقائمة المشتركين عند كل بناء للشاشة
    List<Subscriber> filtered = subscribers;
    if (_showSearch && _searchQuery.isNotEmpty) {
      filtered = subscribers
          .where(
            (sub) =>
                sub.name.contains(_searchQuery) ||
                sub.phone.contains(_searchQuery) ||
                sub.user.contains(_searchQuery),
          )
          .toList();
    }
    // ترتيب القائمة حسب الخيار المختار
    filtered = List<Subscriber>.from(filtered);
    _sortSubscribers(filtered);
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية مثل شاشة تسجيل الدخول
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: Column(
              children: [
                // الأزرار الأفقية (تظهر دائماً عند وجود مشتركين)
                if (filtered.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // زر البحث
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(
                                alpha: isDark ? 0.1 : 0.2,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: IconButton(
                              icon: Icon(
                                _showSearch ? Icons.search_off : Icons.search,
                                color: colorScheme.onPrimary,
                              ),
                              tooltip: _showSearch ? 'إخفاء البحث' : 'البحث',
                              onPressed: () {
                                setState(() {
                                  _showSearch = !_showSearch;
                                  if (!_showSearch) {
                                    _searchQuery = '';
                                    _searchController.clear();
                                  }
                                });
                              },
                            ),
                          ),
                        ),

                        // زر التصفية
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(
                                alpha: isDark ? 0.1 : 0.2,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.filter_list,
                                color: colorScheme.onPrimary,
                              ),
                              tooltip: 'تصفية',
                              onPressed: () {
                                _showFilterBottomSheet(
                                  context,
                                  colorScheme,
                                  isDark,
                                );
                              },
                            ),
                          ),
                        ),

                        // زر الإضافة
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.only(left: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(
                                alpha: isDark ? 0.1 : 0.2,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.person_add,
                                color: colorScheme.onPrimary,
                              ),
                              tooltip: 'إضافة مشترك',
                              onPressed: () async {
                                await _handleAddSubscriber();
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // رأس الشاشة (يظهر فقط عندما تكون القائمة فارغة)
                if (filtered.isEmpty) _buildHeader(colorScheme, isDark),

                // شريط البحث (يظهر ويختفي)
                if (filtered.isNotEmpty) _buildSearchBar(colorScheme, isDark),
                if (_showSearch) const SizedBox(height: 16),

                // محتوى الشاشة
                Expanded(
                  child: isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : Column(
                          children: [
                            // المحتوى الرئيسي
                            Expanded(
                              child: RefreshIndicator(
                                onRefresh: () async {
                                  await _fetchSubscribers();
                                },
                                child: (_showSearch && _searchQuery.isEmpty)
                                    ? const SizedBox.shrink()
                                    : filtered.isEmpty
                                    ? _buildEmptyState(colorScheme)
                                    : _buildSubscribersList(
                                        filtered,
                                        colorScheme,
                                        isDark,
                                      ),
                              ),
                            ),
                          ],
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء حالة الشاشة الفارغة
  Widget _buildEmptyState(ColorScheme colorScheme) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.all(24),
      child: Card(
        elevation: 0,
        color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.people_outline,
                size: 48,
                color: colorScheme.primary.withValues(alpha: 0.6),
              ),
              const SizedBox(height: 20),
              Text(
                _showSearch && _searchQuery.isNotEmpty
                    ? 'لا يوجد نتائج'
                    : 'لا يوجد مشتركين مضافين',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              if (!(_showSearch && _searchQuery.isNotEmpty)) ...[
                const SizedBox(height: 12),
                // زر المزامنة - يفتح شاشة المصادر
                ElevatedButton.icon(
                  onPressed: () {
                    // فحص إذا كان الحساب منتهي
                    if (widget.isTrialExpired == true) {
                      _showAccountExpiredDialog();
                      return;
                    }

                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (_) => const SourcesScreen()),
                    );
                  },
                  icon: const Icon(Icons.sync),
                  label: const Text('مزامنة البيانات'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
              ] else ...[
                const SizedBox(height: 8),
              ],
              Text(
                _showSearch && _searchQuery.isNotEmpty
                    ? 'جرب البحث بكلمات مختلفة'
                    : 'أضف مشترك جديد للبدء في إدارة الاشتراكات',
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // دالة لبناء قائمة المشتركين
  Widget _buildSubscribersList(
    List<Subscriber> filtered,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
        borderRadius: BorderRadius.circular(22),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(22),
        child: ListView.builder(
          itemCount: filtered.length,
          itemBuilder: (context, i) {
            final sub = filtered[i];
            final subscription = _subscriptionsMap[sub.subscriptionId];
            final subscriptionName = subscription != null
                ? subscription['name'] ?? ''
                : '';
            // تحديد لون خلفية الحقل بالكامل حسب حالة الاشتراك والوضع الليلي
            final colorScheme = Theme.of(context).colorScheme;
            Color tileBgColor;
            final now = DateTime.now();
            final remaining = sub.endDate.difference(now);
            if (remaining.inSeconds <= 0) {
              tileBgColor = colorScheme.errorContainer.withValues(
                alpha: colorScheme.brightness == Brightness.dark ? 0.22 : 0.18,
              );
            } else if (remaining.inDays <= 2) {
              tileBgColor = colorScheme.secondaryContainer.withValues(
                alpha: colorScheme.brightness == Brightness.dark ? 0.22 : 0.18,
              );
            } else {
              tileBgColor = colorScheme.primaryContainer.withValues(
                alpha: colorScheme.brightness == Brightness.dark ? 0.18 : 0.13,
              );
            }
            return Column(
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: tileBgColor,
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(
                      color: colorScheme.outline.withValues(alpha: 0.1),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.shadow.withValues(alpha: 0.08),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(18),
                      onTap: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => SubscriberDetailsScreen(
                              subscriber: sub,
                              repository: widget.repository,
                            ),
                          ),
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 10,
                          ),
                          leading: showNano
                              ? Icon(
                                  Icons.wifi,
                                  size: 24,
                                  color: sub.isOnline
                                      ? const Color(0xFF43A047) // أخضر واضح
                                      : colorScheme.onSurface.withValues(
                                          alpha: 0.32,
                                        ), // رمادي باهت
                                )
                              : null,
                          title: Row(
                            children: [
                              Expanded(
                                child: Row(
                                  children: [
                                    Flexible(
                                      child: Text(
                                        sub.name,
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: sub.isDeletedStatus
                                              ? colorScheme.error
                                              : colorScheme.onSurface,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    // إضافة مؤشر نوع المشترك
                                    if (sub.isManualSubscriber)
                                      Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.blue.withValues(
                                            alpha: 0.2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Text(
                                          'يدوي',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.blue,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      )
                                    else if (sub.isSasSubscriber)
                                      Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withValues(
                                            alpha: 0.2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Text(
                                          'SAS',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.green,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      )
                                    else if (sub.isEarthlinkSubscriber)
                                      Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.withValues(
                                            alpha: 0.2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        child: Text(
                                          'Earthlink',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.orange,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    if (sub.isDeletedStatus)
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          right: 6,
                                        ),
                                        child: Tooltip(
                                          message: 'محذوف من السيرفر',
                                          child: Icon(
                                            Icons.delete_forever,
                                            color: colorScheme.error,
                                            size: 20,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              // زر جرس قرب انتهاء الاشتراك: أصفر إذا لم يُرسل اليوم، رمادي إذا أُرسل اليوم
                              Builder(
                                builder: (context) {
                                  final show =
                                      _showExpireSoonIcon &&
                                      sub.endDate
                                              .difference(DateTime.now())
                                              .inSeconds >
                                          0 &&
                                      sub.endDate
                                              .difference(DateTime.now())
                                              .inHours <
                                          48 &&
                                      sub.phone
                                          .replaceAll(RegExp(r'[^0-9]'), '')
                                          .isNotEmpty;
                                  final sentToday = _isExpireSoonSentToday(sub);
                                  if (show) {
                                    return IconButton(
                                      icon: Icon(
                                        Icons.notifications_active,
                                        color: sentToday
                                            ? colorScheme.outline
                                            : Colors.amber,
                                        size: 26,
                                      ),
                                      tooltip: sentToday
                                          ? 'تم إرسال التنبيه اليوم'
                                          : 'إرسال تنبيه قرب انتهاء الاشتراك (مرة واحدة يومياً)',
                                      onPressed: sentToday
                                          ? () {
                                              debugPrint(
                                                'تم الضغط على أيقونة تنبيه قرب الانتهاء الرمادية للمشترك: ${sub.name}',
                                              );
                                              _showAlreadySentExpireMessage(
                                                sub.name,
                                              );
                                            }
                                          : () {
                                              debugPrint(
                                                'تم الضغط على أيقونة تنبيه قرب الانتهاء الصفراء للمشترك: ${sub.name}',
                                              );
                                              _sendExpireSoonMsg(sub);
                                            },
                                    );
                                  } else {
                                    return const SizedBox.shrink();
                                  }
                                },
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 3,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                      .withValues(alpha: 0.18),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.calendar_today,
                                      size: 15,
                                      color: Theme.of(
                                        context,
                                      ).colorScheme.primary,
                                    ),
                                    const SizedBox(width: 3),
                                    Text(
                                      _daysAndHours(sub),
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: _daysAndHours(sub) == 'منتهي'
                                            ? Theme.of(
                                                context,
                                              ).colorScheme.error
                                            : Theme.of(
                                                context,
                                              ).colorScheme.onSurface,
                                        fontWeight:
                                            _daysAndHours(sub) == 'منتهي'
                                            ? FontWeight.bold
                                            : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          subtitle: Padding(
                            padding: const EdgeInsets.only(top: 6),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.credit_card,
                                        size: 18,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                      ),
                                      const SizedBox(width: 3),
                                      Flexible(
                                        child: Text(
                                          subscriptionName,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // أيقونة تذكير الديون قبل الخط الفاصل
                                if (_showDebtReminderIcon &&
                                    sub.totalDebt > 0 &&
                                    sub.phone
                                        .replaceAll(RegExp(r'[^0-9]'), '')
                                        .isNotEmpty) ...[
                                  Builder(
                                    builder: (context) {
                                      final sentToday =
                                          _isDebtReminderSentToday(sub);
                                      debugPrint(
                                        'عرض أيقونة تذكير الدين - المشترك: ${sub.name}, تم الإرسال اليوم: $sentToday',
                                      );

                                      return GestureDetector(
                                        onTap: sentToday
                                            ? () => _showAlreadySentMessage(
                                                sub.name,
                                              )
                                            : () => _sendDebtReminderMsg(sub),
                                        child: Tooltip(
                                          message: sentToday
                                              ? 'تم إرسال تذكير الديون اليوم'
                                              : 'تذكير المشترك بالديون (مرة واحدة يومياً)',
                                          child: Container(
                                            margin: const EdgeInsets.symmetric(
                                              horizontal: 4,
                                            ),
                                            child: Icon(
                                              Icons.warning_amber_rounded,
                                              color: sentToday
                                                  ? Theme.of(
                                                      context,
                                                    ).colorScheme.outline
                                                  : Colors.amber,
                                              size: 18,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                                // زر تجديد سريع
                                GestureDetector(
                                  onTap: () async {
                                    // فتح شاشة تجديد الاشتراك
                                    final result =
                                        await showModalBottomSheet<bool>(
                                          context: context,
                                          isScrollControlled: true,
                                          backgroundColor: Colors.transparent,
                                          builder: (context) => Container(
                                            height:
                                                MediaQuery.of(
                                                  context,
                                                ).size.height *
                                                0.9,
                                            decoration: const BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(20),
                                                topRight: Radius.circular(20),
                                              ),
                                            ),
                                            child: RenewSubscriptionBottomSheet(
                                              subscriber: sub,
                                              repository: widget.repository,
                                            ),
                                          ),
                                        );

                                    // إذا تم التجديد بنجاح، أعد تحميل القائمة
                                    if (result == true) {
                                      _fetchSubscribers();
                                      if (mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                              'تم تجديد الاشتراك بنجاح',
                                            ),
                                            backgroundColor: Colors.green,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                  child: Tooltip(
                                    message: 'تجديد الاشتراك',
                                    child: Container(
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 4,
                                      ),
                                      child: Icon(
                                        Icons.refresh_rounded,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                        size: 18,
                                      ),
                                    ),
                                  ),
                                ),
                                Container(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                  ),
                                  width: 1,
                                  height: 18,
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.outlineVariant,
                                ),
                                Row(
                                  children: [
                                    if (sub.notes != null &&
                                        sub.notes!.startsWith('wallet:')) ...[
                                      Icon(
                                        Icons.account_balance_wallet,
                                        size: 16,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.secondary,
                                      ),
                                      const SizedBox(width: 3),
                                      Text(
                                        'رصيد: ${sub.notes!.replaceFirst('wallet:', '').split(' ')[0]} د.ع',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Theme.of(
                                            context,
                                          ).colorScheme.secondary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ] else if (sub.totalDebt > 0) ...[
                                      const SizedBox(width: 3),
                                      Text(
                                        'دين: ${sub.totalDebt.toStringAsFixed(0)} د.ع',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Theme.of(
                                            context,
                                          ).colorScheme.error,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ] else ...[
                                      Icon(
                                        Icons.check_circle,
                                        size: 16,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                      ),
                                      const SizedBox(width: 3),
                                      Text(
                                        'لا توجد ديون',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Theme.of(
                                            context,
                                          ).colorScheme.primary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                          onTap: () async {
                            final result = await Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => SubscriberDetailsScreen(
                                  subscriber: sub,
                                  repository: widget.repository,
                                ),
                              ),
                            );
                            if (result == true) {
                              _fetchSubscribers();
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('تم تجديد الاشتراك بنجاح'),
                                    backgroundColor: Colors.green,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// إظهار رسالة انتهاء الحساب للميزات المحظورة
  void _showAccountExpiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.lock_clock, color: Colors.orange, size: 28),
              const SizedBox(width: 12),
              const Text(
                'حساب منتهي الصلاحية',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'هذه الميزة متاحة للحسابات المفعلة فقط.\nيرجى تفعيل حسابك للوصول إلى جميع الميزات.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.upgrade, size: 18),
              label: const Text('تفعيل الحساب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () async {
                Navigator.of(context).pop();
                // الانتقال مباشرة إلى شاشة الحساب للتفعيل
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SupabaseAccountScreen(
                      onImageChanged: (file) {
                        // لا حاجة لفعل شيء هنا
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
