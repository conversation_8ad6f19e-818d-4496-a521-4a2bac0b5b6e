-- ===================================================================
-- إعداد سريع لنظام التحديثات في Supabase
-- نسخ والصق هذا الكود في Supabase SQL Editor
-- ===================================================================

-- 1. إنشاء جدول التحديثات الرئيسي
CREATE TABLE IF NOT EXISTS app_updates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    build_number INTEGER NOT NULL,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('android', 'ios', 'web')),
    is_active BOOLEAN DEFAULT true,
    is_forced BOOLEAN DEFAULT false,
    min_compatible_version VARCHAR(20),
    database_migration_required BOOLEAN DEFAULT false,
    release_notes TEXT,
    download_url TEXT,
    features JSONB DEFAULT '[]'::jsonb,
    bug_fixes JSONB DEFAULT '[]'::jsonb,
    security_updates JSONB DEFAULT '[]'::jsonb,
    file_size_mb DECIMAL(10,2),
    checksum VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id),
    
    UNIQUE(version, build_number, platform)
);

-- 2. إنشاء جدول سجل تحديثات المستخدمين
CREATE TABLE IF NOT EXISTS user_update_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    update_id UUID NOT NULL REFERENCES app_updates(id) ON DELETE CASCADE,
    from_version VARCHAR(20) NOT NULL,
    to_version VARCHAR(20) NOT NULL,
    update_type VARCHAR(20) NOT NULL CHECK (update_type IN ('automatic', 'manual', 'forced')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('started', 'completed', 'failed', 'skipped')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    device_info JSONB,
    backup_created BOOLEAN DEFAULT false,
    data_migrated BOOLEAN DEFAULT false
);

-- 3. إنشاء جدول تفضيلات التحديث
CREATE TABLE IF NOT EXISTS user_update_preferences (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    auto_check_enabled BOOLEAN DEFAULT true,
    auto_download_enabled BOOLEAN DEFAULT false,
    wifi_only_download BOOLEAN DEFAULT true,
    notification_enabled BOOLEAN DEFAULT true,
    skipped_versions JSONB DEFAULT '[]'::jsonb,
    last_check_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. إنشاء جدول إحصائيات التحديثات
CREATE TABLE IF NOT EXISTS update_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    update_id UUID NOT NULL REFERENCES app_updates(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    total_users INTEGER DEFAULT 0,
    successful_updates INTEGER DEFAULT 0,
    failed_updates INTEGER DEFAULT 0,
    skipped_updates INTEGER DEFAULT 0,
    average_download_time_seconds INTEGER,
    average_install_time_seconds INTEGER,
    date DATE DEFAULT CURRENT_DATE,
    
    UNIQUE(update_id, platform, date)
);

-- 5. إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_update_history_user_id ON user_update_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_update_history_update_id ON user_update_history(update_id);
CREATE INDEX IF NOT EXISTS idx_user_update_history_status ON user_update_history(status);
CREATE INDEX IF NOT EXISTS idx_update_statistics_update_id ON update_statistics(update_id);
CREATE INDEX IF NOT EXISTS idx_update_statistics_date ON update_statistics(date);
CREATE INDEX IF NOT EXISTS idx_app_updates_platform_active ON app_updates(platform, is_active);
CREATE INDEX IF NOT EXISTS idx_app_updates_build_number ON app_updates(build_number);
CREATE INDEX IF NOT EXISTS idx_app_updates_published_at ON app_updates(published_at);

-- 6. إنشاء الدوال المساعدة
CREATE OR REPLACE FUNCTION get_latest_update(
    user_platform TEXT,
    current_build_number INTEGER DEFAULT 0
)
RETURNS TABLE (
    version TEXT,
    build_number INTEGER,
    is_forced BOOLEAN,
    is_compatible BOOLEAN,
    release_notes TEXT,
    download_url TEXT,
    features JSONB,
    bug_fixes JSONB,
    security_updates JSONB,
    database_migration_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        au.version::TEXT,
        au.build_number,
        au.is_forced,
        CASE 
            WHEN au.min_compatible_version IS NULL THEN true
            ELSE au.build_number >= current_build_number
        END as is_compatible,
        au.release_notes,
        au.download_url,
        au.features,
        au.bug_fixes,
        au.security_updates,
        au.database_migration_required
    FROM app_updates au
    WHERE au.platform = user_platform
        AND au.is_active = true
        AND au.build_number > current_build_number
        AND (au.published_at IS NULL OR au.published_at <= NOW())
    ORDER BY au.build_number DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. دالة للتحقق من صلاحيات الإدارة
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_accounts 
        WHERE user_id = auth.uid() 
        AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة تنفيذ SQL (مطلوبة للتطبيق)
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS TEXT AS $$
BEGIN
    EXECUTE sql;
    RETURN 'تم تنفيذ الأمر بنجاح';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'خطأ: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. تفعيل RLS على الجداول
ALTER TABLE app_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_update_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_update_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE update_statistics ENABLE ROW LEVEL SECURITY;

-- 10. إنشاء السياسات الأمنية

-- سياسات app_updates
CREATE POLICY "Users can view active published updates" ON app_updates
    FOR SELECT USING (
        is_active = true 
        AND (published_at IS NULL OR published_at <= NOW())
    );

CREATE POLICY "Admins can manage all updates" ON app_updates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_accounts 
            WHERE user_id = auth.uid() 
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- سياسات user_update_history
CREATE POLICY "Users can view their own update history" ON user_update_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own update history" ON user_update_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own update history" ON user_update_history
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسات user_update_preferences
CREATE POLICY "Users can manage their own update preferences" ON user_update_preferences
    FOR ALL USING (auth.uid() = user_id);

-- سياسات update_statistics
CREATE POLICY "Admins can view update statistics" ON update_statistics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_accounts 
            WHERE user_id = auth.uid() 
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- 11. منح الصلاحيات
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON app_updates TO authenticated;
GRANT ALL ON user_update_history TO authenticated;
GRANT ALL ON user_update_preferences TO authenticated;
GRANT SELECT ON update_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION get_latest_update(TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin_user() TO authenticated;
GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;

-- منح صلاحيات لـ service role (تطبيق الإدارة)
GRANT ALL ON app_updates TO service_role;
GRANT ALL ON user_update_history TO service_role;
GRANT ALL ON user_update_preferences TO service_role;
GRANT ALL ON update_statistics TO service_role;

-- منح صلاحيات على التسلسلات
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- 12. إدراج تحديث تجريبي للاختبار
INSERT INTO app_updates (
    version,
    build_number,
    platform,
    is_active,
    is_forced,
    min_compatible_version,
    database_migration_required,
    release_notes,
    download_url,
    features,
    bug_fixes,
    security_updates,
    published_at
) VALUES (
    '1.1.0',
    2,
    'android',
    true,
    false,
    '1.0.0',
    false,
    'تحديث تجريبي مع تحسينات في الأداء وإصلاحات للأخطاء',
    'https://example.com/itower-v1.1.0.apk',
    '["تحسين واجهة المستخدم", "إضافة ميزة البحث المتقدم", "تحسين سرعة التطبيق"]'::jsonb,
    '["إصلاح مشكلة تسجيل الدخول", "إصلاح مشكلة المزامنة", "إصلاح مشاكل الأداء"]'::jsonb,
    '["تحسين أمان البيانات", "إصلاح ثغرات أمنية"]'::jsonb,
    NOW()
) ON CONFLICT (version, build_number, platform) DO NOTHING;

-- رسالة تأكيد
SELECT 'تم إعداد نظام التحديثات بنجاح! ✅' as status;
