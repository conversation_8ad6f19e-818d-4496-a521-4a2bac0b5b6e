import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite/sqflite.dart';
import 'simple_root_screen.dart';
import 'features/subscribers/domain/subscribers_repository.dart';
import 'features/subscribers/domain/subscribers_repository_impl.dart';
import 'features/subscribers/data/subscribers_storage_impl.dart';
import 'features/main_home_screen.dart';
import 'db_helper.dart';
import 'dart:async';
import 'utils/api_helper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'services/account_service.dart';
import 'screens/internet_check_screen.dart';
// النظام الموحد الجديد
import 'core/services/unified_initialization_service.dart';
import 'core/managers/unified_account_status_manager.dart';
import 'core/managers/unified_restriction_manager.dart';

// تحديث حالة الاتصال العامة (أي سيرفر أو لوحة متصل)
Future<void> _updateAnyDeviceConnected() async {
  final servers = await DBHelper.instance.getAllServers();
  final boards = await DBHelper.instance.getAllBoards();
  final anyConnected =
      servers.any(
        (s) =>
            s['connected'] == 1 ||
            s['connected'] == true ||
            s['connected'] == '1',
      ) ||
      boards.any(
        (b) =>
            b['connected'] == 1 ||
            b['connected'] == true ||
            b['connected'] == '1',
      );
  anyDeviceConnectedNotifier.value = anyConnected;
}

// تعريف RouteObserver ليكون متاحًا للتطبيق كله
final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

// تعريف navigatorKey عالمي للوصول للـ context في أي مكان
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// تعريف ValueNotifier عالمي لحالة الاتصال بأي سيرفر أو لوحة
final ValueNotifier<bool> anyDeviceConnectedNotifier = ValueNotifier(false);
final ValueNotifier<bool> syncCompletedNotifier = ValueNotifier(false);

void main() async {
  debugPrint('🚀 [MAIN] بدء تهيئة التطبيق الموحد...');

  // تشغيل التطبيق مع معالج الأخطاء العام
  runZonedGuarded(
    () async {
      try {
        // تهيئة Flutter
        WidgetsFlutterBinding.ensureInitialized();
        debugPrint('✅ [MAIN] تم تهيئة Flutter');

        // تهيئة قاعدة البيانات للمنصات المختلفة
        if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
          sqfliteFfiInit();
          databaseFactory = databaseFactoryFfi;
          debugPrint('✅ [MAIN] تم تهيئة قاعدة البيانات للديسكتوب');
        }

        // تهيئة النظام الموحد
        await UnifiedInitializationService.initialize();
        debugPrint('✅ [MAIN] تم تهيئة النظام الموحد');

        // بدء التطبيق
        runApp(const MyApp());
      } catch (e, stack) {
        debugPrint('❌ [MAIN] خطأ في تهيئة التطبيق: $e');
        debugPrint('Stack trace: $stack');

        // طباعة تقرير حالة النظام للتشخيص
        try {
          UnifiedInitializationService.printSystemStatus();
        } catch (_) {}

        runApp(
          MaterialApp(
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'خطأ في تهيئة التطبيق',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      e.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          await UnifiedInitializationService.reinitialize();
                          // إعادة تشغيل التطبيق
                          runApp(const MyApp());
                        } catch (retryError) {
                          debugPrint('❌ فشل في إعادة التهيئة: $retryError');
                        }
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    },
    (error, stackTrace) {
      // معالج الأخطاء العام
      debugPrint('❌ [MAIN] خطأ غير معالج في التطبيق: $error');
      debugPrint('Stack trace: $stackTrace');
    },
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // حالة التطبيق المبسطة
  bool _internetChecked = false;

  // المتغيرات الأساسية
  late final SubscribersRepository subscribersRepository;
  ThemeMode themeMode = ThemeMode.system;

  // متغيرات المزامنة المبسطة
  Timer? _autoSyncTimer;
  bool _isSyncing = false;

  void _toggleTheme() async {
    debugPrint(
      'تم استدعاء _toggleTheme. القيمة الحالية: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    setState(() {
      if (themeMode == ThemeMode.dark) {
        themeMode = ThemeMode.light;
      } else {
        themeMode = ThemeMode.dark;
      }
    });
    debugPrint(
      'تم تغيير themeMode إلى: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('themeMode', themeMode.toString().split('.').last);
  }

  void _showThemeStatus(BuildContext context) {
    String text;
    if (themeMode == ThemeMode.dark) {
      text = 'تم تفعيل الوضع الليلي';
    } else {
      text = 'تم تفعيل الوضع النهاري';
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(text, textAlign: TextAlign.center),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    subscribersRepository = SubscribersRepositoryImpl(SubscribersStorageImpl());
    _initThemeMode();
    // تم نقل منطق التحقق من تسجيل الدخول إلى SimpleRootScreen
  }

  // تم نقل منطق المزامنة إلى النظام الموحد

  // تم نقل منطق فحص الحساب إلى النظام الموحد

  void _retryConnectBoards() async {
    final boards = await DBHelper.instance.getAllBoards();
    final connectedBoards = boards
        .where(
          (b) =>
              b['connected'] == 1 ||
              b['connected'] == true ||
              b['connected'] == '1',
        )
        .toList();
    if (connectedBoards.isEmpty) return;

    debugPrint('[RETRY_CONNECT] فحص ${connectedBoards.length} لوحة متصلة...');

    // فحص التوكنات أولاً قبل إعادة المحاولة
    bool needsRetry = false;
    for (final board in connectedBoards) {
      final token = board['token'];
      if (!isTokenValid(token)) {
        needsRetry = true;
        debugPrint('[RETRY_CONNECT] اللوحة ${board['name']} تحتاج تجديد توكن');
        break;
      }
    }

    if (!needsRetry) {
      debugPrint(
        '[RETRY_CONNECT] جميع التوكنات صالحة، لا حاجة لإعادة المحاولة',
      );
      return;
    }

    // إعادة المحاولة فقط عند الحاجة، وبفترات أطول
    Future.delayed(const Duration(minutes: 30), () async {
      await _retryConnectOnce(connectedBoards);
      // المحاولة الثانية بعد ساعة من الأولى
      Future.delayed(const Duration(minutes: 30), () async {
        await _retryConnectOnce(connectedBoards);
      });
    });
  }

  Future<void> _retryConnectOnce(List boards) async {
    for (final board in boards) {
      try {
        if (board['connected'] == 1 ||
            board['connected'] == true ||
            board['connected'] == '1') {
          // فحص صالحية التوكن أولاً
          final token = board['token'];
          if (!isTokenValid(token)) {
            debugPrint('[RETRY_CONNECT] تجديد توكن اللوحة: ${board['name']}');
            // استدعاء tryConnect من boards_list_screen.dart
            await tryConnect(board);
          } else {
            debugPrint('[RETRY_CONNECT] توكن اللوحة ${board['name']} صالح');
          }
        }
      } catch (e) {
        debugPrint(
          '[RETRY CONNECT][ERROR] فشل إعادة الاتصال باللوحة: ${board['name']} - $e',
        );
      }
    }
  }

  Future<void> _autoSyncAllBoards() async {
    debugPrint('[SYNC-DEBUG] _autoSyncAllBoards called');
    if (_isSyncing) {
      debugPrint('[SYNC-DEBUG] _autoSyncAllBoards: already syncing, return');
      return;
    }
    _isSyncing = true;
    try {
      final boards = await DBHelper.instance.getAllBoards();
      for (final board in boards) {
        if (board['connected'] == 1 ||
            board['connected'] == true ||
            board['connected'] == '1') {
          try {
            debugPrint('[SYNC-DEBUG] Syncing board: ${board['name']}');
            // استدعاء دالة المزامنة المركزية لكل لوحة متصلة
            final changes = await syncAllFromServer(
              board: board,
              silent: false,
            );
            debugPrint(
              '[AUTO SYNC] تمت مزامنة اللوحة: \x1B[32m${board['name']}\x1B[0m',
            );
            final ctx = navigatorKey.currentContext;
            if (changes.isNotEmpty && ctx != null && mounted) {
              if (changes.length <= 3) {
                for (final msg in changes) {
                  // إشعار منفصل لكل تغيير
                  ScaffoldMessenger.of(ctx).showSnackBar(
                    SnackBar(
                      content: Text(msg, textAlign: TextAlign.center),
                      backgroundColor: msg.startsWith('تمت إضافة')
                          ? Colors.green
                          : null,
                      duration: const Duration(seconds: 3),
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 40,
                        vertical: 20,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                  );
                  await Future.delayed(const Duration(milliseconds: 500));
                }
              } else {
                // إشعار ملخص واحد
                final summary =
                    'تمت ${changes.length} تغييرات أثناء المزامنة:\n' +
                    changes.take(3).join('\n') +
                    (changes.length > 3 ? '\nوالمزيد...' : '');
                ScaffoldMessenger.of(ctx).showSnackBar(
                  SnackBar(
                    content: Text(summary, textAlign: TextAlign.center),
                    duration: const Duration(seconds: 5),
                    behavior: SnackBarBehavior.floating,
                    margin: const EdgeInsets.symmetric(
                      horizontal: 40,
                      vertical: 20,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                );
              }
            }
          } catch (e) {
            debugPrint(
              '[AUTO SYNC][ERROR] فشل مزامنة اللوحة: ${board['name']} - $e',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('[AUTO SYNC][ERROR] $e');
    } finally {
      _isSyncing = false;
      debugPrint('[SYNC-DEBUG] _autoSyncAllBoards finished, _isSyncing=false');
      // بعد نجاح المزامنة التلقائية، حدث جميع الشاشات المرتبطة
      syncCompletedNotifier.value = true;
      debugPrint(
        '[SYNC-DEBUG] تم اكتمال المزامنة بنجاح عند: ${DateTime.now()}',
      );

      // إعادة تعيين القيمة بعد فترة قصيرة لضمان استقبال الإشعارات المستقبلية
      Future.delayed(Duration(seconds: 2), () {
        syncCompletedNotifier.value = false;
        debugPrint(
          '[SYNC-DEBUG] تم إعادة تعيين syncCompletedNotifier إلى false',
        );
      });
    }
  }

  Future<void> _initThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final mode = prefs.getString('themeMode');
    if (mode == null) {
      // أول تشغيل: اعتمد على النظام
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      setState(() {
        themeMode = brightness == Brightness.dark
            ? ThemeMode.dark
            : ThemeMode.light;
      });
    } else {
      setState(() {
        if (mode == 'dark') {
          themeMode = ThemeMode.dark;
        } else {
          themeMode = ThemeMode.light;
        }
      });
    }
  }

  @override
  void dispose() {
    debugPrint('[MAIN] _MyAppState.dispose called, cleaning up');
    _autoSyncTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // وضع NotificationListener حول MaterialApp لضمان استقبال الإشعارات دائمًا
    return NotificationListener<ServerStatusChangedNotification>(
      onNotification: (notification) {
        _updateAnyDeviceConnected();
        return true;
      },
      child: NotificationListener<BoardStatusChangedNotification>(
        onNotification: (notification) {
          _updateAnyDeviceConnected();
          return true;
        },
        child: MaterialApp(
          navigatorKey: navigatorKey,
          title: 'iTower',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          ),
          darkTheme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.dark,
            ),
            brightness: Brightness.dark,
          ),
          themeMode: themeMode,
          locale: const Locale('ar', ''),
          supportedLocales: const [Locale('ar', '')],
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          navigatorObservers: [routeObserver],
          home: Directionality(
            textDirection: TextDirection.rtl,
            child: _internetChecked
                ? SimpleRootScreen(
                    themeMode: themeMode,
                    onToggleTheme: _toggleTheme,
                    showThemeStatus: _showThemeStatus,
                    onLoginSuccess: () {
                      debugPrint(
                        '[MAIN] تم تسجيل الدخول بنجاح - النظام الموحد سيتولى الباقي',
                      );
                    },
                    onLogout: () {
                      debugPrint('[MAIN] تم تسجيل الخروج - تنظيف البيانات');
                      _autoSyncTimer?.cancel();

                      // ✅ إعادة بناء SimpleRootScreen بدون حلقة
                      setState(() {
                        // فقط إعادة بناء الواجهة
                      });
                    },
                  )
                : InternetCheckScreen(
                    onInternetConnected: _onInternetConnected,
                  ),
          ),
        ),
      ),
    );
  }

  // تحديث حالة الاتصال العامة (أي سيرفر أو لوحة متصل)
  Future<void> _updateAnyDeviceConnected() async {
    final servers = await DBHelper.instance.getAllServers();
    final boards = await DBHelper.instance.getAllBoards();
    final anyConnected =
        servers.any(
          (s) =>
              s['connected'] == 1 ||
              s['connected'] == true ||
              s['connected'] == '1',
        ) ||
        boards.any(
          (b) =>
              b['connected'] == 1 ||
              b['connected'] == true ||
              b['connected'] == '1',
        );
    anyDeviceConnectedNotifier.value = anyConnected;
  }

  // منطق مركزي لتغيير التدرج اللوني بناءً على الرسائل في جميع الشاشات
  void updateGradientByMessage(String? message) {
    if (message == 'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
      anyDeviceConnectedNotifier.value = true;
    } else if (message == 'تم قطع الاتصال مع السيرفر') {
      anyDeviceConnectedNotifier.value = false;
    }
  }

  /// معالج عند نجاح الاتصال بالإنترنت
  Future<void> _onInternetConnected() async {
    debugPrint('🌐 [MAIN] تم تأكيد الاتصال بالإنترنت - بدء تهيئة التطبيق');

    try {
      // تهيئة Supabase
      debugPrint('🔧 [MAIN] تهيئة Supabase...');
      await Supabase.initialize(
        url: 'https://iwtvsvfqmafsziqnoekm.supabase.co',
        anonKey:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.9ho9O3Bk-iziVQBSiQ5X5V9E45KqEzJ2tuMoSu5kNKg',
      );
      debugPrint('✅ [MAIN] تم تهيئة Supabase بنجاح');

      // تهيئة مدير حالة الحساب الموحد
      debugPrint('👤 [MAIN] تهيئة مدير حالة الحساب الموحد...');
      // سيتم تهيئة UnifiedAccountStatusManager من خلال UnifiedInitializationService
      debugPrint('✅ [MAIN] سيتم تهيئة مدير حالة الحساب من النظام الموحد');

      // تحديث حالة الاتصال
      await _updateAnyDeviceConnected();

      // الانتقال للشاشة الرئيسية
      if (mounted) {
        setState(() {
          _internetChecked = true;
        });
      }

      debugPrint('🎉 [MAIN] تم تهيئة التطبيق بنجاح');
    } catch (e) {
      debugPrint('❌ [MAIN] خطأ في تهيئة التطبيق: $e');

      // في حالة الخطأ، إظهار رسالة خطأ
      if (mounted && navigatorKey.currentContext != null) {
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          SnackBar(
            content: Text('خطأ في تهيئة التطبيق: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}
