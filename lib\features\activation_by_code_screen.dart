import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/account_service.dart';

class ActivationByCodeScreen extends StatefulWidget {
  const ActivationByCodeScreen({Key? key}) : super(key: key);

  @override
  State<ActivationByCodeScreen> createState() => _ActivationByCodeScreenState();
}

class _ActivationByCodeScreenState extends State<ActivationByCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMsg;

  Future<void> _activateWithCode() async {
    setState(() {
      _isLoading = true;
      _errorMsg = null;
    });

    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      setState(() {
        _errorMsg = 'يجب تسجيل الدخول أولاً';
        _isLoading = false;
      });
      return;
    }

    final code = _codeController.text.trim();
    if (code.isEmpty) {
      setState(() {
        _errorMsg = 'يرجى إدخال كود التفعيل';
        _isLoading = false;
      });
      return;
    }

    try {
      // ✅ استخدام دالة تفعيل الكود الجديدة والمحسنة
      final result = await AccountService.activateCode(user.id, code);

      if (result == null || result['success'] != true) {
        setState(() {
          _errorMsg = result?['error'] ?? 'فشل في تفعيل الكود';
          _isLoading = false;
        });
        return;
      }

      // تحديث القيم محلياً
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isTrial', false);

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        // عرض رسالة نجاح مع تفاصيل التفعيل
        final subscriptionType = result['subscription_type'] ?? 'غير محدد';
        final durationDays = result['duration_days'] ?? 0;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تفعيل الحساب بنجاح!\nنوع الاشتراك: $subscriptionType\nالمدة: $durationDays يوم',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _errorMsg = 'فشل التفعيل: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية مثل شاشة تسجيل الدخول
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 30), // مساحة للـ AppBar
                  // شعار دائري عصري مع أيقونة المفتاح
                  Container(
                    margin: const EdgeInsets.only(bottom: 32),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withValues(alpha: 0.18),
                          blurRadius: 24,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 64,
                      backgroundColor: Colors.white.withValues(
                        alpha: isDark ? 0.08 : 0.18,
                      ),
                      child: Icon(
                        Icons.vpn_key_rounded,
                        color: colorScheme.primary,
                        size: 64,
                      ),
                    ),
                  ),

                  // عنوان رئيسي
                  Text(
                    'أدخل كود التفعيل',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onPrimary,
                      letterSpacing: 1,
                      shadows: [
                        Shadow(
                          color: colorScheme.shadow.withValues(alpha: 0.13),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // وصف
                  Text(
                    'أدخل الكود الذي حصلت عليه لتفعيل حسابك',
                    style: TextStyle(
                      fontSize: 18,
                      color: colorScheme.onPrimary.withValues(alpha: 0.92),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),

                  // بطاقة شفافة عصرية للمحتوى
                  Card(
                    elevation: 0,
                    color: colorScheme.surface.withValues(
                      alpha: isDark ? 0.7 : 0.93,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 32,
                        horizontal: 24,
                      ),
                      child: Column(
                        children: [
                          // حقل إدخال الكود
                          Container(
                            decoration: BoxDecoration(
                              color: isDark
                                  ? colorScheme.surface.withValues(alpha: 0.8)
                                  : Colors.white.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: colorScheme.shadow.withValues(
                                    alpha: 0.1,
                                  ),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: TextField(
                              controller: _codeController,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2,
                              ),
                              decoration: InputDecoration(
                                labelText: 'كود التفعيل',
                                labelStyle: TextStyle(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                                prefixIcon: Icon(
                                  Icons.confirmation_number_rounded,
                                  color: colorScheme.primary,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16),
                                  borderSide: BorderSide.none,
                                ),
                                filled: true,
                                fillColor: Colors.transparent,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 20,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),

                          // رسالة الخطأ
                          if (_errorMsg != null)
                            Container(
                              padding: const EdgeInsets.all(16),
                              margin: const EdgeInsets.only(bottom: 24),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.red.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.error_rounded,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      _errorMsg!,
                                      style: const TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          // زر التفعيل
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 18,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                elevation: 3,
                              ),
                              icon: _isLoading
                                  ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Icon(Icons.vpn_key_rounded, size: 22),
                              label: Text(
                                _isLoading ? 'جاري التفعيل...' : 'تفعيل الحساب',
                              ),
                              onPressed: _isLoading ? null : _activateWithCode,
                            ),
                          ),
                          const SizedBox(height: 20),

                          // نص إرشادي
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.blue.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_rounded,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'تأكد من إدخال الكود بشكل صحيح كما تم إرساله إليك',
                                    style: TextStyle(
                                      color: Colors.blue.shade700,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
