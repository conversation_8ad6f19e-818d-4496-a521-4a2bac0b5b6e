import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'features/main_home_screen.dart';
import 'features/app_introduction_screen.dart';
import 'supabase_login_screen.dart';
import 'services/time_sync_service.dart';
import 'services/account_service.dart';

import 'services/introduction_service.dart';
import 'services/deep_link_service.dart';
import 'services/app_migration_service.dart';
import 'tools/system_audit_tool.dart';
import 'db_helper.dart';
import 'services/deleted_account_detector.dart';
import 'services/update_manager.dart';
import 'services/database_setup_service.dart';
// النظام الموحد الجديد
import 'core/managers/unified_account_status_manager.dart';
import 'core/managers/unified_restriction_manager.dart';

class SimpleRootScreen extends StatefulWidget {
  final ThemeMode? themeMode;
  final VoidCallback? onToggleTheme;
  final void Function(BuildContext context)? showThemeStatus;
  final VoidCallback? onLoginSuccess;
  final VoidCallback? onLogout;

  const SimpleRootScreen({
    super.key,
    this.themeMode,
    this.onToggleTheme,
    this.showThemeStatus,
    this.onLoginSuccess,
    this.onLogout,
  });

  @override
  State<SimpleRootScreen> createState() => _SimpleRootScreenState();
}

class _SimpleRootScreenState extends State<SimpleRootScreen> {
  bool _loading = true;
  // تم حذف المتغيرات المهجورة - استخدم AccountStatusManager بدلاً من ذلك
  bool _localLogin = false;
  SharedPreferences? _prefs;
  String? _errorMessage;
  bool _hasNetworkError = false;
  bool _showIntroduction = false;
  Timer? _logoutCheckTimer;

  @override
  void initState() {
    super.initState();
    _initializeApp();

    // تهيئة Deep Links بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      DeepLinkService().initialize(context);
    });
  }

  @override
  void dispose() {
    // إيقاف مراقبة الحساب المحذوف
    DeletedAccountDetector.stopMonitoring();
    // إيقاف مؤقت فحص تسجيل الخروج
    _logoutCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _initializeApp() async {
    try {
      setState(() {
        _errorMessage = null;
        _hasNetworkError = false;
      });

      // تحميل SharedPreferences أولاً
      _prefs = await SharedPreferences.getInstance();
      _localLogin = _prefs!.getBool('is_logged_in') ?? false;

      // تهيئة المستخدم في النظام الموحد إذا كان مسجل دخول
      if (_localLogin) {
        final userId = _prefs!.getString('user_id');
        if (userId != null) {
          await UnifiedAccountStatusManager.updateUserId(userId);
        }
      }

      // تسجيل مفصل لحالة SharedPreferences
      debugPrint('[SIMPLE-ROOT] 🔍 فحص SharedPreferences:');
      debugPrint(
        '[SIMPLE-ROOT]   is_logged_in: ${_prefs!.getBool('is_logged_in')}',
      );
      debugPrint('[SIMPLE-ROOT]   user_id: ${_prefs!.getString('user_id')}');
      debugPrint(
        '[SIMPLE-ROOT]   user_email: ${_prefs!.getString('user_email')}',
      );
      debugPrint(
        '[SIMPLE-ROOT]   جميع المفاتيح: ${_prefs!.getKeys().toList()}',
      );

      // إعداد جدول حالة الحساب
      try {
        debugPrint('[SIMPLE-ROOT] إعداد جدول حالة الحساب...');
        await DBHelper.instance.createAccountStatusTableIfNotExists();
        debugPrint('[SIMPLE-ROOT] ✅ تم إعداد جدول حالة الحساب بنجاح');

        // تنظيف البيانات القديمة - سيتم التعامل معها من النظام الموحد
        debugPrint('[SIMPLE-ROOT] تنظيف البيانات القديمة من النظام الموحد');
      } catch (e) {
        debugPrint('[SIMPLE-ROOT] خطأ في إعداد جدول حالة الحساب: $e');
      }

      // فحص إذا كان هناك طلب تسجيل خروج معلق بسبب حساب محذوف
      final pendingLogout =
          await DeletedAccountDetector.checkForPendingLogout();
      if (pendingLogout) {
        debugPrint(
          '🚨 [ROOT] تم العثور على طلب تسجيل خروج معلق - إعادة توجيه لتسجيل الدخول',
        );
        setState(() {
          _loading = false;
          _localLogin = false;
        });
        return;
      }

      // فحص حالة الاستعراض
      final hasSeenIntro = await IntroductionService.hasSeenIntroduction();

      if (!hasSeenIntro) {
        setState(() {
          _showIntroduction = true;
          _loading = false;
        });
        return;
      }

      // تأخير قصير للسماح لـ Deep Links بالعمل قبل فحص الجلسة
      await Future.delayed(const Duration(milliseconds: 500));

      // ثم فحص الجلسة
      await _checkSession();
    } catch (e) {
      debugPrint('خطأ في تهيئة التطبيق: $e');
      setState(() {
        _loading = false;
        _errorMessage = _getErrorMessage(e);
        _hasNetworkError = _isNetworkError(e);
      });
    }
  }

  String _getErrorMessage(dynamic error) {
    final errorStr = error.toString().toLowerCase();

    if (errorStr.contains('network') ||
        errorStr.contains('connection') ||
        errorStr.contains('timeout') ||
        errorStr.contains('socket')) {
      return 'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
    }

    if (errorStr.contains('auth') || errorStr.contains('unauthorized')) {
      return 'مشكلة في المصادقة. يرجى تسجيل الدخول مرة أخرى.';
    }

    if (errorStr.contains('supabase') || errorStr.contains('database')) {
      return 'مشكلة في الخادم. يرجى المحاولة لاحقاً.';
    }

    return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  }

  bool _isNetworkError(dynamic error) {
    final errorStr = error.toString().toLowerCase();
    return errorStr.contains('network') ||
        errorStr.contains('connection') ||
        errorStr.contains('timeout') ||
        errorStr.contains('socket');
  }

  // تم نقل فحص حالة الحساب إلى النظام الموحد

  void _scheduleSyncOperations(dynamic user) {
    // تأجيل المزامنة لمدة ثانية واحدة بعد عرض الشاشة
    Future.delayed(const Duration(seconds: 1), () {
      if (!mounted) return;
      _performBackgroundSync(user);
    });
  }

  Future<void> _performBackgroundSync(dynamic user) async {
    try {
      debugPrint('[SIMPLE-ROOT] بدء المزامنة في الخلفية...');

      // إصلاح تلقائي للحسابات التجريبية المحولة خطأً إلى expired
      try {
        await AccountService.autoFixTrialAccounts();
      } catch (e) {
        debugPrint('[SIMPLE-ROOT] ⚠️ خطأ في الإصلاح التلقائي: $e');
      }

      // مزامنة الوقت مع الخادم (إذا توفر الإنترنت)
      try {
        await TimeSyncService.syncTimeWithServer();
        debugPrint('[SIMPLE-ROOT] ✅ تم مزامنة الوقت مع الخادم');
      } catch (e) {
        debugPrint('[SIMPLE-ROOT] ⚠️ خطأ في مزامنة الوقت: $e');
      }

      // مزامنة بيانات الحساب مع Supabase
      if (user != null) {
        try {
          await TimeSyncService.syncAccountData();
          debugPrint('[SIMPLE-ROOT] ✅ تم مزامنة بيانات الحساب');
        } catch (e) {
          debugPrint('[SIMPLE-ROOT] ⚠️ خطأ في مزامنة البيانات: $e');
        }
      }

      debugPrint('[SIMPLE-ROOT] انتهت المزامنة في الخلفية');

      // بدء مراقبة الحساب المحذوف
      DeletedAccountDetector.startMonitoring();

      // تهيئة النظام الجديد
      try {
        debugPrint('[SIMPLE-ROOT] تهيئة النظام الجديد...');
        final migrationSuccess =
            await AppMigrationService.initializeNewSystem();
        if (migrationSuccess) {
          debugPrint('[SIMPLE-ROOT] ✅ تم تهيئة النظام الجديد بنجاح');
        } else {
          debugPrint('[SIMPLE-ROOT] ⚠️ فشل في تهيئة النظام الجديد');
        }
      } catch (e) {
        debugPrint('[SIMPLE-ROOT] ❌ خطأ في تهيئة النظام الجديد: $e');
      }

      // فحص سريع للنظام (في وضع التطوير فقط)
      if (kDebugMode) {
        try {
          debugPrint('[SIMPLE-ROOT] إجراء فحص سريع للنظام...');
          await SystemAuditTool.runQuickCheck();
        } catch (e) {
          debugPrint('[SIMPLE-ROOT] ❌ خطأ في الفحص السريع: $e');
        }
      }

      // إشعار بسيط للمستخدم (اختياري)
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم تحديث البيانات'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('[SIMPLE-ROOT] خطأ عام في المزامنة: $e');

      // إشعار بالخطأ (فقط للأخطاء المهمة)
      if (mounted && !_isNetworkError(e)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('فشل في تحديث بعض البيانات'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  Future<void> _checkSession() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;

      debugPrint('🔍 [SIMPLE_ROOT] فحص الجلسة - المستخدم: ${user?.id}');
      debugPrint('🔍 [SIMPLE_ROOT] تسجيل دخول محلي: $_localLogin');

      if (user == null && !_localLogin) {
        debugPrint('🔍 [SIMPLE_ROOT] لا يوجد مستخدم - عرض شاشة تسجيل الدخول');
        setState(() => _loading = false);
        if (widget.onLogout != null) widget.onLogout!();
        return;
      }

      // تأجيل المزامنة إلى بعد عرض الشاشة
      _scheduleSyncOperations(user);

      // تحديث معرف المستخدم في النظام الموحد
      await UnifiedAccountStatusManager.updateUserId(user?.id);

      // تطبيق القيود حسب حالة الحساب (النظام الموحد)
      await UnifiedRestrictionManager.applySystemRestrictions();

      // تم حذف تحديث المتغيرات المهجورة - استخدم AccountStatusManager مباشرة

      setState(() => _loading = false);

      // بدء المزامنة إذا لم تنته الصلاحية
      if (!UnifiedAccountStatusManager.isRestricted &&
          widget.onLoginSuccess != null) {
        widget.onLoginSuccess!();
      }

      // بدء التحقق الدوري من علامة تسجيل الخروج
      _startLogoutCheck();

      // إعداد قاعدة بيانات التحديثات (في وضع التطوير فقط)
      if (kDebugMode) {
        try {
          debugPrint('[SIMPLE-ROOT] إعداد قاعدة بيانات التحديثات...');
          final setupSuccess = await DatabaseSetupService.setupUpdateTables();
          if (setupSuccess) {
            debugPrint('[SIMPLE-ROOT] ✅ تم إعداد قاعدة بيانات التحديثات بنجاح');
          } else {
            debugPrint('[SIMPLE-ROOT] ⚠️ فشل في إعداد قاعدة بيانات التحديثات');
          }
        } catch (e) {
          debugPrint('[SIMPLE-ROOT] ❌ خطأ في إعداد قاعدة بيانات التحديثات: $e');
        }
      }

      // فحص التحديثات عند بدء التطبيق
      if (mounted) {
        UpdateManager.checkForUpdatesOnStartup(context);
      }
    } catch (e) {
      debugPrint('خطأ في فحص الجلسة: $e');
      setState(() => _loading = false);
    }
  }

  /// بدء التحقق الدوري من علامة تسجيل الخروج الفوري
  void _startLogoutCheck() {
    _logoutCheckTimer?.cancel();
    _logoutCheckTimer = Timer.periodic(const Duration(seconds: 2), (
      timer,
    ) async {
      try {
        final pendingLogout =
            await DeletedAccountDetector.checkForPendingLogout();
        if (pendingLogout && mounted) {
          debugPrint('🚨 [ROOT] تم اكتشاف طلب تسجيل خروج فوري - إعادة توجيه');
          timer.cancel();

          setState(() {
            _loading = false;
            _localLogin = false;
          });

          if (widget.onLogout != null) {
            widget.onLogout!();
          }
        }
      } catch (e) {
        debugPrint('❌ [ROOT] خطأ في فحص علامة تسجيل الخروج: $e');
      }
    });
  }

  void _onIntroductionCompleted() {
    setState(() {
      _showIntroduction = false;
      _loading = true;
    });
    // بدء فحص الجلسة بعد انتهاء الاستعراض
    _checkSession();
  }

  @override
  Widget build(BuildContext context) {
    // عرض شاشة الاستعراض
    if (_showIntroduction) {
      return AppIntroductionScreen(onCompleted: _onIntroductionCompleted);
    }

    // عرض شاشة التحميل
    if (_loading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'جاري تحميل التطبيق...',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
        ),
      );
    }

    // عرض شاشة الخطأ مع إعادة المحاولة
    if (_errorMessage != null) {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _hasNetworkError ? Icons.wifi_off : Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _loading = true;
                      _errorMessage = null;
                    });
                    _initializeApp();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة المحاولة'),
                ),
                if (_hasNetworkError) ...[
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () {
                      // السماح بالمتابعة بدون اتصال
                      setState(() {
                        _loading = false;
                        _errorMessage = null;
                        _localLogin = _prefs?.getBool('is_logged_in') ?? false;
                      });
                    },
                    child: const Text('المتابعة بدون اتصال'),
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }

    // التحقق من تسجيل الدخول
    final user = Supabase.instance.client.auth.currentUser;

    if (user == null && !_localLogin) {
      if (widget.onLogout != null) widget.onLogout!();
      return const SupabaseLoginScreen();
    }

    // عرض الشاشة الرئيسية
    return MainHomeScreen(
      themeMode: widget.themeMode,
      onToggleTheme: widget.onToggleTheme,
      showThemeStatus: widget.showThemeStatus,
      isTrialExpired: UnifiedAccountStatusManager.isRestricted,
    );
  }
}
