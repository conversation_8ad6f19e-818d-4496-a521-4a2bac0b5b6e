import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/unified_account_model.dart';
import 'unified_initialization_service.dart';
import '../managers/unified_account_status_manager.dart';
import '../managers/unified_restriction_manager.dart';

/// خدمة المراقبة الموحدة
/// تراقب جميع أجزاء النظام وتكتشف المشاكل مبكراً
class UnifiedMonitoringService {
  static const String _tag = '[UNIFIED_MONITORING]';
  
  // المتحكم في أحداث المراقبة
  static final StreamController<MonitoringEvent> _eventController = 
      StreamController<MonitoringEvent>.broadcast();
  
  // حالة المراقبة
  static bool _isMonitoring = false;
  static Timer? _healthCheckTimer;
  static Timer? _performanceTimer;
  
  // إحصائيات النظام
  static final Map<String, dynamic> _systemStats = {};
  static final List<MonitoringEvent> _recentEvents = [];
  static const int _maxRecentEvents = 100;
  
  // الخصائص العامة
  static Stream<MonitoringEvent> get eventStream => _eventController.stream;
  static bool get isMonitoring => _isMonitoring;
  static Map<String, dynamic> get systemStats => Map.from(_systemStats);
  static List<MonitoringEvent> get recentEvents => List.from(_recentEvents);
  
  /// بدء المراقبة
  static Future<void> startMonitoring() async {
    if (_isMonitoring) return;
    
    debugPrint('$_tag بدء مراقبة النظام...');
    
    try {
      _isMonitoring = true;
      
      // بدء فحص الصحة الدوري
      _startHealthCheck();
      
      // بدء مراقبة الأداء
      _startPerformanceMonitoring();
      
      // مراقبة تغييرات حالة الحساب
      _monitorAccountStatus();
      
      _logEvent(MonitoringEventType.systemStart, 'بدء مراقبة النظام');
      debugPrint('$_tag تم بدء المراقبة بنجاح');
      
    } catch (e) {
      debugPrint('$_tag خطأ في بدء المراقبة: $e');
      _isMonitoring = false;
      rethrow;
    }
  }
  
  /// إيقاف المراقبة
  static void stopMonitoring() {
    debugPrint('$_tag إيقاف مراقبة النظام');
    
    _isMonitoring = false;
    _healthCheckTimer?.cancel();
    _performanceTimer?.cancel();
    
    _logEvent(MonitoringEventType.systemStop, 'إيقاف مراقبة النظام');
  }
  
  /// بدء فحص الصحة الدوري
  static void _startHealthCheck() {
    _healthCheckTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _performHealthCheck(),
    );
    
    // فحص أولي
    _performHealthCheck();
  }
  
  /// إجراء فحص الصحة
  static void _performHealthCheck() {
    try {
      final healthCheck = UnifiedInitializationService.performHealthCheck();
      
      _systemStats['lastHealthCheck'] = DateTime.now().toIso8601String();
      _systemStats['overallHealth'] = healthCheck['overallHealth'];
      _systemStats['components'] = healthCheck['components'];
      
      // فحص المشاكل
      final issues = _detectHealthIssues(healthCheck);
      
      if (issues.isNotEmpty) {
        for (final issue in issues) {
          _logEvent(MonitoringEventType.healthIssue, issue);
        }
      } else {
        _logEvent(MonitoringEventType.healthCheck, 'فحص الصحة: النظام سليم');
      }
      
    } catch (e) {
      _logEvent(MonitoringEventType.error, 'خطأ في فحص الصحة: $e');
    }
  }
  
  /// اكتشاف مشاكل الصحة
  static List<String> _detectHealthIssues(Map<String, dynamic> healthCheck) {
    final issues = <String>[];
    
    // فحص الحالة العامة
    if (healthCheck['overallHealth'] != 'healthy') {
      issues.add('الحالة العامة للنظام: ${healthCheck['overallHealth']}');
    }
    
    // فحص المكونات
    final components = healthCheck['components'] as Map<String, dynamic>?;
    if (components != null) {
      for (final entry in components.entries) {
        final component = entry.value as Map<String, dynamic>?;
        if (component != null) {
          // فحص مكونات غير مهيأة
          if (component['isInitialized'] == false) {
            issues.add('المكون ${entry.key} غير مهيأ');
          }
          
          // فحص مكونات بدون بيانات
          if (component['hasStatus'] == false || component['hasData'] == false) {
            issues.add('المكون ${entry.key} لا يحتوي على بيانات');
          }
        }
      }
    }
    
    return issues;
  }
  
  /// بدء مراقبة الأداء
  static void _startPerformanceMonitoring() {
    _performanceTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => _collectPerformanceMetrics(),
    );
    
    // جمع أولي
    _collectPerformanceMetrics();
  }
  
  /// جمع مقاييس الأداء
  static void _collectPerformanceMetrics() {
    try {
      final now = DateTime.now();
      
      // إحصائيات الذاكرة (تقديرية)
      _systemStats['performanceCheck'] = {
        'timestamp': now.toIso8601String(),
        'eventsCount': _recentEvents.length,
        'systemUptime': _calculateUptime(),
      };
      
      // فحص الأداء
      final performanceIssues = _detectPerformanceIssues();
      
      if (performanceIssues.isNotEmpty) {
        for (final issue in performanceIssues) {
          _logEvent(MonitoringEventType.performanceIssue, issue);
        }
      }
      
    } catch (e) {
      _logEvent(MonitoringEventType.error, 'خطأ في مراقبة الأداء: $e');
    }
  }
  
  /// اكتشاف مشاكل الأداء
  static List<String> _detectPerformanceIssues() {
    final issues = <String>[];
    
    // فحص عدد الأحداث
    if (_recentEvents.length > _maxRecentEvents * 0.8) {
      issues.add('عدد الأحداث مرتفع: ${_recentEvents.length}');
    }
    
    // فحص الأحداث المتكررة
    final errorEvents = _recentEvents
        .where((e) => e.type == MonitoringEventType.error)
        .toList();
    
    if (errorEvents.length > 10) {
      issues.add('عدد أخطاء مرتفع: ${errorEvents.length}');
    }
    
    return issues;
  }
  
  /// مراقبة تغييرات حالة الحساب
  static void _monitorAccountStatus() {
    UnifiedAccountStatusManager.statusStream.listen(
      (status) {
        if (status != null) {
          _logEvent(
            MonitoringEventType.accountStatusChange,
            'تغيير حالة الحساب: ${status.status.name} (${status.dataSource})',
          );
          
          // تحديث الإحصائيات
          _systemStats['currentAccountStatus'] = {
            'status': status.status.name,
            'isTrial': status.isTrial,
            'trialDaysRemaining': status.trialDaysRemaining,
            'dataSource': status.dataSource,
            'lastUpdated': status.updatedAt.toIso8601String(),
          };
        } else {
          _logEvent(
            MonitoringEventType.accountStatusChange,
            'تم مسح بيانات الحساب',
          );
          _systemStats.remove('currentAccountStatus');
        }
      },
      onError: (error) {
        _logEvent(
          MonitoringEventType.error,
          'خطأ في مراقبة حالة الحساب: $error',
        );
      },
    );
  }
  
  /// تسجيل حدث
  static void _logEvent(MonitoringEventType type, String message) {
    final event = MonitoringEvent(
      type: type,
      message: message,
      timestamp: DateTime.now(),
    );
    
    // إضافة للقائمة
    _recentEvents.add(event);
    
    // الحفاظ على الحد الأقصى
    if (_recentEvents.length > _maxRecentEvents) {
      _recentEvents.removeAt(0);
    }
    
    // إشعار المستمعين
    _eventController.add(event);
    
    // طباعة في وضع التطوير
    if (kDebugMode) {
      debugPrint('$_tag [${type.name.toUpperCase()}] $message');
    }
  }
  
  /// حساب وقت التشغيل
  static String _calculateUptime() {
    // هذا تقدير بسيط - يمكن تحسينه
    final startTime = _recentEvents.isNotEmpty 
        ? _recentEvents.first.timestamp 
        : DateTime.now();
    
    final uptime = DateTime.now().difference(startTime);
    
    if (uptime.inDays > 0) {
      return '${uptime.inDays} أيام، ${uptime.inHours % 24} ساعات';
    } else if (uptime.inHours > 0) {
      return '${uptime.inHours} ساعات، ${uptime.inMinutes % 60} دقائق';
    } else {
      return '${uptime.inMinutes} دقائق';
    }
  }
  
  /// تسجيل حدث مخصص
  static void logCustomEvent(String message, {MonitoringEventType? type}) {
    _logEvent(type ?? MonitoringEventType.info, message);
  }
  
  /// تسجيل خطأ
  static void logError(String error, {String? context}) {
    final message = context != null ? '$context: $error' : error;
    _logEvent(MonitoringEventType.error, message);
  }
  
  /// تسجيل تحذير
  static void logWarning(String warning) {
    _logEvent(MonitoringEventType.warning, warning);
  }
  
  /// الحصول على تقرير شامل
  static Map<String, dynamic> getComprehensiveReport() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'isMonitoring': _isMonitoring,
      'systemStats': _systemStats,
      'recentEventsCount': _recentEvents.length,
      'eventsByType': _getEventsByType(),
      'healthSummary': _getHealthSummary(),
      'performanceSummary': _getPerformanceSummary(),
    };
  }
  
  /// تجميع الأحداث حسب النوع
  static Map<String, int> _getEventsByType() {
    final eventsByType = <String, int>{};
    
    for (final event in _recentEvents) {
      final typeName = event.type.name;
      eventsByType[typeName] = (eventsByType[typeName] ?? 0) + 1;
    }
    
    return eventsByType;
  }
  
  /// ملخص الصحة
  static Map<String, dynamic> _getHealthSummary() {
    final lastHealthCheck = _systemStats['lastHealthCheck'];
    final overallHealth = _systemStats['overallHealth'];
    
    return {
      'lastCheck': lastHealthCheck,
      'status': overallHealth ?? 'unknown',
      'issuesDetected': _recentEvents
          .where((e) => e.type == MonitoringEventType.healthIssue)
          .length,
    };
  }
  
  /// ملخص الأداء
  static Map<String, dynamic> _getPerformanceSummary() {
    final performanceCheck = _systemStats['performanceCheck'] as Map<String, dynamic>?;
    
    return {
      'lastCheck': performanceCheck?['timestamp'],
      'uptime': performanceCheck?['systemUptime'],
      'eventsCount': performanceCheck?['eventsCount'],
      'errorRate': _calculateErrorRate(),
    };
  }
  
  /// حساب معدل الأخطاء
  static double _calculateErrorRate() {
    if (_recentEvents.isEmpty) return 0.0;
    
    final errorCount = _recentEvents
        .where((e) => e.type == MonitoringEventType.error)
        .length;
    
    return (errorCount / _recentEvents.length) * 100;
  }
  
  /// إنهاء الخدمة
  static void dispose() {
    stopMonitoring();
    _eventController.close();
    _systemStats.clear();
    _recentEvents.clear();
  }
}

/// نوع حدث المراقبة
enum MonitoringEventType {
  systemStart,
  systemStop,
  healthCheck,
  healthIssue,
  performanceIssue,
  accountStatusChange,
  error,
  warning,
  info,
}

/// حدث المراقبة
class MonitoringEvent {
  final MonitoringEventType type;
  final String message;
  final DateTime timestamp;
  
  const MonitoringEvent({
    required this.type,
    required this.message,
    required this.timestamp,
  });
  
  @override
  String toString() {
    return '[${type.name.toUpperCase()}] ${timestamp.toIso8601String()}: $message';
  }
}
