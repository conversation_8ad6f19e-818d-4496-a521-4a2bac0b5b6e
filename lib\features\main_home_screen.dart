import 'package:flutter/material.dart';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import '../db_helper.dart';
import '../core/managers/internet_status_manager.dart';
import '../core/managers/unified_account_status_manager.dart';
import '../core/managers/unified_restriction_manager.dart';
import '../core/models/unified_account_model.dart';
import 'subscribers/presentation/subscribers_list_screen.dart';
import 'devices_screen.dart';
import '../settings_page.dart';
import '../main.dart'; // لاستيراد anyDeviceConnectedNotifier فقط
import 'subscribers/data/subscriber_model.dart';
import 'subscribers/domain/subscribers_repository_impl.dart';
import 'subscribers/data/subscribers_storage_impl.dart';
import 'subscribers/domain/subscribers_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/account_service.dart';
import '../services/deleted_account_detector.dart';
import '../services/user_presence_service.dart';
import 'supabase_account_screen.dart';

// تعريف Repository افتراضي فارغ لتجنب الأخطاء في حالة null
class DummySubscribersRepository implements SubscribersRepository {
  @override
  Future<List<Subscriber>> getAllSubscribers({int? boardId}) async => [];

  @override
  Future<void> addSubscriber(Subscriber subscriber) async {}

  @override
  Future<void> updateSubscriber(Subscriber subscriber) async {}

  @override
  Future<void> deleteSubscriber(int id) async {}

  @override
  Future<void> payDebt(int subscriberId, double amount, {String? note}) async {}

  @override
  Future<Subscriber?> getSubscriberById(int id) async => null;
}

class MainHomeScreen extends StatefulWidget {
  // رقم الدعم الفني الرئيسي الموحد
  static const String supportPhone = '*************';
  final SubscribersRepository? repository;
  final ThemeMode themeMode;
  final VoidCallback? onToggleTheme;
  final void Function(BuildContext context)? showThemeStatus;
  final bool isTrialExpired;

  const MainHomeScreen({
    Key? key,
    this.repository,
    ThemeMode? themeMode,
    this.onToggleTheme,
    this.showThemeStatus,
    this.isTrialExpired = false,
  }) : themeMode = themeMode ?? ThemeMode.light,
       super(key: key);

  @override
  State<MainHomeScreen> createState() => _MainHomeScreenState();
}

class _MainHomeScreenState extends State<MainHomeScreen> {
  File? _profileImageFile;
  // تم إزالة منطق Firebase القديم - الآن يتم التحكم من SimpleRootScreen
  late final StreamSubscription _connectivitySubscription;
  bool _isNetworkConnected =
      InternetStatusManager.isConnected; // ✅ البدء بالحالة الفعلية
  int _currentIndex = 0;
  int _totalSubscribers = 0;
  double _totalDebt = 0;
  int _activeSubscribers = 0;
  int _expiredSubscribers = 0;
  int _debtSubscribers = 0;
  int _connectedSubscribers = 0;
  DateTime? _lastStatsSync;
  static const Duration statsSyncInterval = Duration(
    minutes: 5,
  ); // مدة المزامنة

  String? _userName;

  // متغيرات الرصيد
  Map<String, dynamic>? _boardBalance;
  bool _isLoadingBalance = false;

  // متغيرات التايمر
  Timer? _balanceTimer; // تايمر جلب الرصيد العادي
  Timer? _boardCheckTimer; // تايمر فحص وجود لوحة متصلة
  bool _hasBoardConnected = false; // حالة وجود لوحة متصلة

  // الفلتر الآن Map<String, dynamic> لمرونة أكبر
  Map<String, dynamic>? _currentFilter;

  @override
  void initState() {
    debugPrint('[MAIN-HOME] initState - بدء تحميل الشاشة الرئيسية');

    try {
      debugPrint('[MAIN-HOME] تحميل صورة الملف الشخصي...');
      _loadProfileImage();

      debugPrint('[MAIN-HOME] استدعاء super.initState...');
      super.initState();

      // تم إزالة منطق Firebase - الآن يتم التحكم من SimpleRootScreen

      debugPrint('[MAIN-HOME] إضافة مستمع المزامنة...');
      syncCompletedNotifier.addListener(_onSyncCompletedListener);

      debugPrint('[MAIN-HOME] إعداد مراقب الاتصال...');
      // مراقبة تغير الاتصال بالإنترنت باستخدام النظام الجديد
      _connectivitySubscription = InternetStatusManager.statusStream.listen((
        isConnected,
      ) async {
        if (mounted) {
          setState(() => _isNetworkConnected = isConnected);
          if (isConnected) {
            debugPrint('[MAIN-HOME] الشبكة متصلة - تحديث الرصيد');
            _loadBoardBalance();
          } else {
            InternetStatusManager.showConnectionStatus(context);
          }
        }
      });

      // ✅ فحص فوري للحالة الأولية للإنترنت
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (mounted) {
          // التأكد من تهيئة مدير الإنترنت أولاً
          if (!InternetStatusManager.isInitialized) {
            debugPrint('[MAIN-HOME] تهيئة مدير الإنترنت...');
            await InternetStatusManager.initialize();
          }

          final currentStatus = InternetStatusManager.isConnected;
          if (currentStatus != _isNetworkConnected) {
            setState(() => _isNetworkConnected = currentStatus);
            debugPrint(
              '[MAIN-HOME] تحديث حالة الإنترنت الأولية: $currentStatus',
            );
          }

          // ✅ فحص فوري للحساب المحذوف
          final user = Supabase.instance.client.auth.currentUser;
          if (user != null) {
            debugPrint('[MAIN-HOME] فحص فوري للحساب المحذوف...');
            final accountExists =
                await DeletedAccountDetector.quickAccountCheck();
            if (!accountExists) {
              debugPrint(
                '[MAIN-HOME] تم اكتشاف حساب محذوف - سيتم تسجيل الخروج',
              );
            }
          }
        }
      });

      // فحص إذا كان الحساب منتهي وتوجيه فوري للشاشة الرئيسية
      if (_isTrialExpired()) {
        debugPrint('[MAIN-HOME] الحساب منتهي - توجيه فوري للشاشة الرئيسية');

        // قطع اتصال اللوحات إذا كان الحساب منتهي
        _disconnectAllBoardsForExpiredAccount();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _currentIndex = 0; // الشاشة الرئيسية
              _currentFilter = null;
            });
          }
        });
      }

      // تحميل الإحصائيات واسم المستخدم
      debugPrint('[MAIN-HOME] تحميل الإحصائيات...');
      _loadStats();

      // تحميل رصيد اللوحة
      debugPrint('[MAIN-HOME] تحميل رصيد اللوحة...');
      _loadBoardBalance();

      // بدء نظام التايمر الذكي للرصيد
      _startSmartBalanceTimer();

      debugPrint('[MAIN-HOME] انتهى initState بنجاح');
    } catch (e, stackTrace) {
      debugPrint('[MAIN-HOME] خطأ في initState: $e');
      debugPrint('[MAIN-HOME] Stack trace: $stackTrace');
    }
  }

  // تم حذف _initDeviceIdAndAccount - الآن يتم التحكم من SimpleRootScreen

  // تم حذف _getDeviceId - لم تعد مطلوبة

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تحديث الرصيد عند العودة للشاشة
    debugPrint(
      '[MAIN-HOME] didChangeDependencies - تحديث الرصيد عند العودة للشاشة',
    );

    // فحص إذا كان الحساب منتهي وتوجيه فوري للشاشة الرئيسية
    if (_isTrialExpired() && _currentIndex != 0 && _currentIndex != 3) {
      debugPrint('[MAIN-HOME] الحساب منتهي - إعادة توجيه للشاشة الرئيسية');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _currentIndex = 0; // الشاشة الرئيسية
            _currentFilter = null;
          });
        }
      });
    }

    _loadBoardBalance();
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    syncCompletedNotifier.removeListener(_onSyncCompletedListener);
    // إلغاء التايمرات
    _balanceTimer?.cancel();
    _boardCheckTimer?.cancel();
    // قطع اتصال خدمة تتبع الحالة
    UserPresenceService.disconnect();
    super.dispose();
  }

  Future<void> _loadProfileImage() async {
    final prefs = await SharedPreferences.getInstance();
    final path = prefs.getString('profileImagePath');
    if (path != null && await File(path).exists()) {
      setState(() {
        _profileImageFile = File(path);
      });
    }
  }

  Future<void> _checkNetworkStatus() async {
    debugPrint('[MAIN-HOME] _checkNetworkStatus - فحص حالة الشبكة');
    try {
      // ✅ التأكد من تهيئة مدير الإنترنت
      if (!InternetStatusManager.isInitialized) {
        debugPrint('[MAIN-HOME] تهيئة مدير الإنترنت في _checkNetworkStatus...');
        await InternetStatusManager.initialize();
      }

      final connected = InternetStatusManager.isConnected;
      debugPrint('[MAIN-HOME] حالة الاتصال: $connected');
      if (mounted) {
        setState(() => _isNetworkConnected = connected);
        // تحديث الرصيد عند تغيير حالة الشبكة
        if (connected) {
          debugPrint('[MAIN-HOME] الشبكة متصلة - تحديث الرصيد');
          _loadBoardBalance();
        } else {
          // عرض حالة الاتصال باستخدام النظام الجديد
          InternetStatusManager.showConnectionStatus(context);
        }
      }
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في فحص الشبكة: $e');
    }
  }

  void _onSyncCompletedListener() {
    debugPrint(
      '[MAIN-HOME] _onSyncCompletedListener - تم استلام إشعار اكتمال المزامنة',
    );
    if (syncCompletedNotifier.value == true) {
      debugPrint(
        '[MAIN-HOME] قيمة syncCompletedNotifier = true، بدء تحديث البيانات',
      );
      _onSyncCompleted();
      // إعادة تعيين القيمة لضمان استقبال الإشعارات المستقبلية
      Future.delayed(Duration(milliseconds: 100), () {
        syncCompletedNotifier.value = false;
        debugPrint(
          '[MAIN-HOME] تم إعادة تعيين syncCompletedNotifier إلى false',
        );
      });
    }
  }

  void _onSyncCompleted() {
    debugPrint(
      '[MAIN-HOME] _onSyncCompleted - تم استلام إشعار اكتمال المزامنة',
    );
    if (mounted) {
      debugPrint('[MAIN-HOME] تحديث الإحصائيات والرصيد بعد المزامنة...');
      _loadStatsIfNeeded(force: true); // عند اكتمال المزامنة، إجباري
      _loadBoardBalance(); // تحديث الرصيد أيضاً
      _checkNetworkStatus(); // فحص حالة الشبكة

      // إعادة تشغيل التايمر إذا لم يكن يعمل
      if (_balanceTimer?.isActive != true &&
          _boardCheckTimer?.isActive != true) {
        debugPrint('[MAIN-HOME] إعادة تشغيل نظام التايمر عند العودة للشاشة');
        _updateTimerStrategy();
      }
    }
  }

  // تم حذف _checkAccountStatusOnStart - الآن يتم التحكم من SimpleRootScreen

  Future<void> _loadStatsIfNeeded({bool force = false}) async {
    // فحص إذا كان المستخدم لا يزال مسجل دخول
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      debugPrint(
        '[MAIN-HOME] لا يوجد مستخدم مسجل دخول - إيقاف تحديث الإحصائيات',
      );
      return;
    }

    final now = DateTime.now();
    if (!force &&
        _lastStatsSync != null &&
        now.difference(_lastStatsSync!) < statsSyncInterval) {
      return;
    }
    await _loadStats();
    _lastStatsSync = now;
  }

  Future<void> _loadStats() async {
    final subs = await DBHelper.instance.getAllSubscribers();
    final now = DateTime.now();

    // عدد كل المشتركين في قاعدة البيانات بدون استثناء
    final allSubs = subs;

    // الفعالين: غير المنتهين
    final activeSubs = allSubs.where((s) => s.endDate.isAfter(now)).toList();
    // المنتهين: المنتهين فعلياً
    final expiredSubs = allSubs
        .where(
          (s) => s.endDate.isBefore(now) || s.endDate.isAtSameMomentAs(now),
        )
        .toList();
    // أصحاب الديون فقط
    final debtSubs = allSubs.where((s) => s.totalDebt > 0).toList();
    // المتصلين فعلياً: onlineStatus == 1 بغض النظر عن صلاحية الاشتراك
    final connectedSubs = allSubs.where((s) => s.onlineStatus == 1).toList();

    // جلب اسم المستخدم من مصادر متعددة
    await _loadUserName();

    setState(() {
      _totalSubscribers = allSubs.length;
      _activeSubscribers = activeSubs.length;
      _expiredSubscribers = expiredSubs.length;
      _debtSubscribers = debtSubs.length;
      _totalDebt = debtSubs.fold(0.0, (sum, s) => sum + s.totalDebt);
      _connectedSubscribers = connectedSubs.length;
    });
  }

  /// تحميل اسم المستخدم من Supabase أو SharedPreferences
  Future<void> _loadUserName() async {
    try {
      // محاولة جلب الاسم من Supabase أولاً
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        final accountData = await AccountService.getAccountData(user.id);
        if (accountData != null) {
          final displayName =
              accountData['display_name'] ??
              user.userMetadata?['display_name'] ??
              user.email?.split('@')[0];

          if (displayName != null && displayName.isNotEmpty) {
            _userName = displayName;

            // حفظ الاسم في SharedPreferences للاستخدام المستقبلي
            final prefs = await SharedPreferences.getInstance();
            await prefs.setString('user_display_name', displayName);
            return;
          }
        }
      }

      // إذا فشل جلب الاسم من Supabase، استخدم SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      _userName =
          prefs.getString('user_display_name') ??
          prefs.getString('userName') ??
          prefs.getString('user_email')?.split('@')[0] ??
          'مستخدم';
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في تحميل اسم المستخدم: $e');

      // في حالة الخطأ، استخدم SharedPreferences كبديل
      final prefs = await SharedPreferences.getInstance();
      _userName =
          prefs.getString('user_display_name') ??
          prefs.getString('userName') ??
          prefs.getString('user_email')?.split('@')[0] ??
          'مستخدم';
    }
  }

  // تحديث حالة الاتصال في MainHomeScreen عند العودة من شاشة السيرفرات
  Future<void> _refreshServerStatus() async {
    setState(() {});
  }

  /// تحميل رصيد اللوحة النشطة
  Future<void> _loadBoardBalance() async {
    debugPrint('[MAIN-HOME] _loadBoardBalance - بدء تحميل رصيد اللوحة');
    if (!mounted) return;

    // فحص إذا كان المستخدم لا يزال مسجل دخول
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null) {
      debugPrint('[MAIN-HOME] لا يوجد مستخدم مسجل دخول - إيقاف تحديث الرصيد');
      return;
    }

    setState(() {
      _isLoadingBalance = true;
    });

    try {
      final balance = await DBHelper.instance.getActiveBoardBalance();
      debugPrint('[MAIN-HOME] تم جلب الرصيد من قاعدة البيانات: $balance');

      // فحص إذا كان هناك لوحة متصلة
      final bool hasBoardNow = balance != null && balance.isNotEmpty;

      if (hasBoardNow != _hasBoardConnected) {
        debugPrint(
          '[MAIN-HOME] تغيير حالة اتصال اللوحة: $_hasBoardConnected -> $hasBoardNow',
        );
        _hasBoardConnected = hasBoardNow;
        _updateTimerStrategy();
      }

      if (mounted) {
        setState(() {
          _boardBalance = balance;
          _isLoadingBalance = false;
        });
        debugPrint('[MAIN-HOME] تم تحديث حالة الرصيد في الواجهة');
      }
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في تحميل رصيد اللوحة: $e');

      // في حالة الخطأ، نعتبر أنه لا توجد لوحة متصلة
      if (_hasBoardConnected) {
        debugPrint(
          '[MAIN-HOME] خطأ في الاتصال - تحديث حالة اللوحة إلى غير متصلة',
        );
        _hasBoardConnected = false;
        _updateTimerStrategy();
      }

      if (mounted) {
        setState(() {
          _isLoadingBalance = false;
        });
      }
    }
  }

  /// بناء ويدجت عرض الرصيد
  Widget _buildBalanceWidget(ColorScheme colorScheme, bool isDark) {
    debugPrint(
      '[MAIN-HOME] _buildBalanceWidget - بناء ويدجت الرصيد: _isLoadingBalance=$_isLoadingBalance, _boardBalance=$_boardBalance',
    );

    if (_isLoadingBalance) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  colorScheme.onPrimary,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'جاري التحميل...',
              style: TextStyle(
                color: colorScheme.onPrimary,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (_boardBalance == null || _boardBalance!['balance_text'] == null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.account_balance_wallet_outlined,
              color: colorScheme.onPrimary.withValues(alpha: 0.7),
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              'غير متوفر',
              style: TextStyle(
                color: colorScheme.onPrimary.withValues(alpha: 0.7),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.account_balance_wallet,
            color: colorScheme.onPrimary,
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            _boardBalance!['balance_text'] ?? 'غير متوفر',
            style: TextStyle(
              color: colorScheme.onPrimary,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// بدء نظام التايمر الذكي للرصيد
  void _startSmartBalanceTimer() {
    debugPrint('[MAIN-HOME] بدء نظام التايمر الذكي للرصيد');

    // تحديد الحالة الأولية
    _loadBoardBalance().then((_) {
      _updateTimerStrategy();
    });
  }

  /// تحديث استراتيجية التايمر حسب حالة اللوحة
  void _updateTimerStrategy() {
    debugPrint(
      '[MAIN-HOME] تحديث استراتيجية التايمر - حالة اللوحة: $_hasBoardConnected',
    );

    // إلغاء التايمرات الحالية
    _balanceTimer?.cancel();
    _boardCheckTimer?.cancel();

    if (_hasBoardConnected) {
      // إذا كانت اللوحة متصلة: تايمر عادي كل 30 ثانية
      debugPrint('[MAIN-HOME] بدء تايمر الرصيد العادي (30 ثانية)');
      _balanceTimer = Timer.periodic(Duration(seconds: 30), (timer) {
        if (mounted && Supabase.instance.client.auth.currentUser != null) {
          // فحص انتهاء الحساب قبل تحديث الرصيد
          _checkAccountStatusBeforeOperation(timer, 'balance');
        } else {
          timer.cancel();
        }
      });
    } else {
      // إذا لم تكن اللوحة متصلة: تايمر فحص كل دقيقتين
      debugPrint('[MAIN-HOME] بدء تايمر فحص اللوحة (120 ثانية)');
      _boardCheckTimer = Timer.periodic(Duration(seconds: 120), (timer) {
        if (mounted && Supabase.instance.client.auth.currentUser != null) {
          // فحص انتهاء الحساب قبل فحص اللوحة
          _checkAccountStatusBeforeOperation(timer, 'board_check');
        } else {
          timer.cancel();
        }
      });
    }
  }

  /// قطع اتصال جميع اللوحات عند انتهاء الحساب
  Future<void> _disconnectAllBoardsForExpiredAccount() async {
    try {
      debugPrint('[MAIN-HOME] قطع اتصال جميع اللوحات - الحساب منتهي الصلاحية');

      // جلب جميع اللوحات وفلترة المتصلة منها
      final allBoards = await DBHelper.instance.getAllBoards();
      final connectedBoards = allBoards
          .where(
            (board) =>
                board['connected'] == 1 ||
                board['connected'] == true ||
                board['connected'] == '1',
          )
          .toList();

      if (connectedBoards.isEmpty) {
        debugPrint('[MAIN-HOME] لا توجد لوحات متصلة للقطع');
        return;
      }

      debugPrint('[MAIN-HOME] عدد اللوحات المتصلة: ${connectedBoards.length}');

      // قطع اتصال كل لوحة
      for (final board in connectedBoards) {
        await _disconnectSingleBoard(board);
      }

      // تحديث حالة اللوحة المحلية
      _hasBoardConnected = false;
      _updateTimerStrategy();

      debugPrint('[MAIN-HOME] تم قطع اتصال جميع اللوحات بنجاح');

      // إرسال إشعار لتحديث واجهة اللوحات
      if (mounted) {
        BoardStatusChangedNotification().dispatch(
          Navigator.of(context, rootNavigator: true).context,
        );
      }

      // إظهار رسالة للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم قطع اتصال جميع اللوحات - الحساب منتهي الصلاحية'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في قطع اتصال اللوحات: $e');
    }
  }

  /// قطع اتصال لوحة واحدة
  Future<void> _disconnectSingleBoard(Map<String, dynamic> board) async {
    try {
      final boardId = board['id'];
      debugPrint(
        '[MAIN-HOME] قطع اتصال اللوحة: ${board['name']} (ID: $boardId)',
      );

      // تحديث حالة اللوحة في قاعدة البيانات
      final updatedBoard = Map<String, dynamic>.from(board);
      updatedBoard['connected'] = 0; // قطع الاتصال
      updatedBoard['token'] = null; // إزالة التوكن

      await DBHelper.instance.updateBoard(boardId, updatedBoard);

      debugPrint('[MAIN-HOME] تم قطع اتصال اللوحة: ${board['name']}');
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في قطع اتصال اللوحة ${board['name']}: $e');
    }
  }

  SubscribersRepository get _effectiveRepository =>
      widget.repository ?? SubscribersRepositoryImpl(SubscribersStorageImpl());

  // فحص إذا انتهت الفترة التجريبية (محدث للنظام الموحد)
  bool _isTrialExpired() {
    // استخدام UnifiedAccountStatusManager كمصدر أساسي
    final isRestricted = UnifiedAccountStatusManager.isRestricted;

    // إذا كان الحساب مقيد وهناك لوحة متصلة، قطع الاتصال
    if (isRestricted && _hasBoardConnected) {
      _disconnectAllBoardsForExpiredAccount();
    }

    return isRestricted;
  }

  /// فحص حالة الحساب من قاعدة البيانات المحلية (محدث للنظام الموحد)
  Future<bool> _isAccountExpiredLocally() async {
    try {
      // استخدام UnifiedAccountStatusManager كمصدر أساسي
      return UnifiedAccountStatusManager.isRestricted;
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في فحص انتهاء الحساب محلياً: $e');
      return false;
    }
  }

  /// فحص إذا كان الحساب محظور من قاعدة البيانات المحلية (محدث للنظام الموحد)
  Future<bool> _isAccountBannedLocally() async {
    try {
      // استخدام النظام الموحد كمصدر أساسي
      final accountStatus = UnifiedAccountStatusManager.currentStatus;
      return accountStatus?.status == AccountStatus.banned ||
          accountStatus?.status == AccountStatus.suspended ||
          accountStatus?.status == AccountStatus.locked;
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في فحص حظر الحساب محلياً: $e');
      return false;
    }
  }

  /// فحص حالة الحساب قبل تنفيذ العمليات في التايمر
  Future<void> _checkAccountStatusBeforeOperation(
    Timer timer,
    String operationType,
  ) async {
    try {
      // فحص الحالة من النظام الموحد
      if (UnifiedAccountStatusManager.isRestricted) {
        debugPrint(
          '[MAIN-HOME] الحساب مقيد (من النظام الموحد) - إيقاف تايمر $operationType',
        );
        timer.cancel();
        if (_hasBoardConnected) {
          _disconnectAllBoardsForExpiredAccount();
        }
        return;
      }

      // فحص الاتصال بالإنترنت
      final hasInternet = InternetStatusManager.isConnected;

      if (!hasInternet) {
        // لا يوجد إنترنت - فحص الحالة المحفوظة محلياً
        final isExpiredLocally = await _isAccountExpiredLocally();
        final isBannedLocally = await _isAccountBannedLocally();

        if (isExpiredLocally || isBannedLocally) {
          debugPrint(
            '[MAIN-HOME] الحساب منتهي/محظور (محلياً) - إيقاف تايمر $operationType',
          );
          timer.cancel();
          if (_hasBoardConnected) {
            _disconnectAllBoardsForExpiredAccount();
          }
          return;
        }
      }

      // تنفيذ العملية المطلوبة
      if (operationType == 'balance') {
        debugPrint('[MAIN-HOME] تحديث دوري للرصيد...');
        _loadBoardBalance();
      } else if (operationType == 'board_check') {
        debugPrint('[MAIN-HOME] فحص وجود لوحة متصلة...');
        _loadBoardBalance();
      }
    } catch (e) {
      debugPrint('[MAIN-HOME] خطأ في فحص حالة الحساب قبل العملية: $e');
      // في حالة الخطأ، تنفيذ العملية العادية
      if (operationType == 'balance') {
        _loadBoardBalance();
      } else if (operationType == 'board_check') {
        _loadBoardBalance();
      }
    }
  }

  // بناء شاشة مقيدة للميزات المحظورة (محدث للنظام الموحد)
  Widget _buildRestrictedScreen(String featureName) {
    // استخدام النظام الموحد لبناء الشاشة المقيدة
    return UnifiedRestrictionManager.buildRestrictedScreen(
      title: 'ميزة مقيدة',
      message:
          UnifiedAccountStatusManager.getRestrictionMessage(featureName) ??
          'لا يمكن الوصول إلى $featureName\nيرجى تفعيل حسابك للمتابعة',
      actionButtonText: 'تفعيل الحساب',
      onActionPressed: () {
        // الانتقال لشاشة الحساب (الإعدادات)
        setState(() => _currentIndex = 3);
      },
    );
  }

  List<Widget> get _screens => [
    // الرئيسية (الإحصائيات)
    _buildHomeStats(),
    // قائمة المشتركين - فحص الصلاحيات باستخدام النظام الموحد
    UnifiedAccountStatusManager.canAccessFeature('subscribers')
        ? SubscribersListScreen(
            repository: _effectiveRepository,
            isDarkMode: widget.themeMode == ThemeMode.dark,
            onToggleTheme: widget.onToggleTheme ?? () {},
            showThemeStatus: widget.showThemeStatus ?? (context) {},
            filter: _currentFilter,
            isTrialExpired: _isTrialExpired(),
          )
        : _buildRestrictedScreen('قائمة المشتركين'),
    // الأجهزة - فحص الصلاحيات باستخدام النظام الموحد
    UnifiedAccountStatusManager.canAccessFeature('devices')
        ? DevicesScreen(key: const ValueKey('devices'))
        : _buildRestrictedScreen('الأجهزة والخوادم'),
    // الإعدادات - متاحة دائماً (تحتوي على شاشة الحساب والتفعيل)
    SettingsPage(
      themeMode: widget.themeMode,
      onToggleTheme: widget.onToggleTheme,
    ),
  ];

  Widget _buildHomeStats() {
    // فحص إذا كان الحساب منتهي
    if (_isTrialExpired()) {
      return _buildExpiredAccountStats();
    }

    return _buildFullStats();
  }

  /// عرض إحصائيات محدودة للحسابات المنتهية
  Widget _buildExpiredAccountStats() {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDark
              ? [
                  Colors.orange.withValues(alpha: 0.3),
                  colorScheme.surface.withValues(alpha: 0.85),
                ]
              : [
                  Colors.orange.withValues(alpha: 0.1),
                  Colors.white.withValues(alpha: 0.95),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة تحذير
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.info_outline, size: 64, color: Colors.orange),
              ),

              const SizedBox(height: 24),

              // رسالة انتهاء الحساب
              Text(
                'انتهت صلاحية حسابك',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              Text(
                'يرجى تفعيل حسابك للوصول إلى جميع الميزات والإحصائيات',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // زر التفعيل
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.upgrade),
                  label: const Text('تفعيل الحساب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () async {
                    // الانتقال إلى شاشة الحساب للتفعيل
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => SupabaseAccountScreen(
                          profileImageFile: _profileImageFile,
                          onImageChanged: (File? newImage) {
                            setState(() {
                              _profileImageFile = newImage;
                            });
                          },
                        ),
                      ),
                    );

                    // إعادة تحميل البيانات بعد العودة
                    await _getAccountDetails();
                    setState(() {});
                  },
                ),
              ),

              const SizedBox(height: 16),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.surface.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.block, color: Colors.red, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'الميزات المحجوبة:',
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• قائمة المشتركين\n• إدارة الأجهزة\n• المزامنة التلقائية\n• الإحصائيات المفصلة',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض الإحصائيات الكاملة للحسابات النشطة
  Widget _buildFullStats() {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final userName = _userName ?? '---';

    return Stack(
      children: [
        // خلفية متدرجة عصرية متناسقة مع شاشة تسجيل الدخول
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [
                      colorScheme.primary.withValues(alpha: 0.9),
                      colorScheme.surface.withValues(alpha: 0.85),
                    ]
                  : [colorScheme.primary, colorScheme.surface],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // رأس الشاشة العصري
                _buildModernHeader(colorScheme, isDark, userName),
                const SizedBox(height: 24),

                // بطاقات الإحصائيات العصرية
                _buildModernStatsGrid(colorScheme, isDark),
                const SizedBox(height: 24),

                // مساحة إضافية في النهاية
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // بناء رأس الشاشة العصري
  Widget _buildModernHeader(
    ColorScheme colorScheme,
    bool isDark,
    String userName,
  ) {
    return Column(
      children: [
        // شريط علوي مع زر الوضع الليلي وعرض الرصيد
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // زر الوضع الليلي
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: IconButton(
                icon: Icon(
                  widget.themeMode == ThemeMode.dark
                      ? Icons.dark_mode_rounded
                      : Icons.light_mode_rounded,
                  color: colorScheme.onPrimary,
                ),
                tooltip: widget.themeMode == ThemeMode.dark
                    ? 'الوضع الليلي'
                    : 'الوضع النهاري',
                onPressed: () {
                  if (widget.onToggleTheme != null) {
                    widget.onToggleTheme!();
                    if (widget.showThemeStatus != null) {
                      widget.showThemeStatus!(context);
                    }
                  }
                },
              ),
            ),

            // عرض الرصيد
            _buildBalanceWidget(colorScheme, isDark),
          ],
        ),

        // شعار دائري عصري مع صورة المستخدم (قابل للضغط) مع حلقة الاتصال
        GestureDetector(
          onTap: () async {
            if (!mounted) return;

            // الانتقال إلى شاشة الحساب بنفس طريقة باقي الشاشات
            await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SupabaseAccountScreen(
                  profileImageFile: _profileImageFile,
                  onImageChanged: (File? newImage) {
                    setState(() {
                      _profileImageFile = newImage;
                    });
                  },
                ),
              ),
            );

            // تحديث اسم المستخدم عند العودة من شاشة الحساب
            if (mounted) {
              await _loadUserName();
              setState(() {}); // إعادة بناء الواجهة لإظهار الاسم المحدث
            }
          },
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              // حلقة الاتصال حول الصورة
              border: Border.all(
                color: _isNetworkConnected
                    ? Colors.green
                    : Colors.grey.withValues(alpha: 0.4),
                width: 4,
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              backgroundImage: _profileImageFile != null
                  ? FileImage(_profileImageFile!)
                  : null,
              child: _profileImageFile == null
                  ? Icon(
                      Icons.person_rounded,
                      color: colorScheme.primary,
                      size: 54,
                    )
                  : null,
            ),
          ),
        ),

        // رسالة ترحيب
        Text(
          'مرحباً، $userName',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'لوحة تحكم احترافية لإدارة المشتركين والديون',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء الإحصائيات بأعمدة أفقية
  Widget _buildModernStatsGrid(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surface.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(
                Icons.analytics_rounded,
                color: colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'إحصائيات سريعة',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // الإحصائيات بأعمدة أفقية
          Row(
            children: [
              // إجمالي المشتركين
              Expanded(
                child: _buildHorizontalStatItem(
                  icon: Icons.people_rounded,
                  label: 'الكل',
                  value: _totalSubscribers.toString(),
                  color: Colors.blue,
                  colorScheme: colorScheme,
                  isDark: isDark,
                  onTap: () {
                    // فحص إذا كان الحساب منتهي
                    if (_isTrialExpired()) {
                      _showAccountExpiredDialog();
                      return;
                    }

                    if (_totalSubscribers == 0) {
                      if (!mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('لا يوجد مشتركين.'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                      return;
                    }
                    setState(() {
                      _currentFilter = null; // كل المشتركين
                      _currentIndex = 1;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),

              // المشتركين النشطين
              Expanded(
                child: _buildHorizontalStatItem(
                  icon: Icons.verified_user_rounded,
                  label: 'نشط',
                  value: _activeSubscribers.toString(),
                  color: Colors.green,
                  colorScheme: colorScheme,
                  isDark: isDark,
                  onTap: () {
                    // فحص إذا كان الحساب منتهي
                    if (_isTrialExpired()) {
                      _showAccountExpiredDialog();
                      return;
                    }

                    setState(() {
                      _currentFilter = {'active': true};
                      _currentIndex = 1;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),

              // المنتهين
              Expanded(
                child: _buildHorizontalStatItem(
                  icon: Icons.schedule_rounded,
                  label: 'منتهي',
                  value: _expiredSubscribers.toString(),
                  color: Colors.red,
                  colorScheme: colorScheme,
                  isDark: isDark,
                  onTap: () async {
                    // فحص إذا كان الحساب منتهي
                    if (_isTrialExpired()) {
                      _showAccountExpiredDialog();
                      return;
                    }

                    if (_expiredSubscribers == 0) {
                      if (!mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('لا يوجد مشتركين منتهين.'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                      return;
                    }
                    setState(() {
                      _currentFilter = {'expired': true};
                      _currentIndex = 1;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),

              // المتصلين
              Expanded(
                child: _buildHorizontalStatItem(
                  icon: Icons.wifi_rounded,
                  label: 'متصل',
                  value: _connectedSubscribers.toString(),
                  color: Colors.purple,
                  colorScheme: colorScheme,
                  isDark: isDark,
                  onTap: () async {
                    // فحص إذا كان الحساب منتهي
                    if (_isTrialExpired()) {
                      _showAccountExpiredDialog();
                      return;
                    }

                    if (_connectedSubscribers == 0) {
                      if (!mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('لا يوجد مشتركين متصلين حالياً.'),
                          backgroundColor: Colors.orange,
                        ),
                      );
                      return;
                    }
                    setState(() {
                      _currentFilter = {'onlineStatus': 1};
                      _currentIndex = 1;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // صف منفصل للديون
          _buildDebtStatRow(colorScheme, isDark),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية تفاعلية حديثة
  Widget _buildInteractiveStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
    required bool isPositive,
    required ColorScheme colorScheme,
    required bool isDark,
    VoidCallback? onTap,
  }) {
    return Container(
      constraints: const BoxConstraints(minHeight: 120, maxHeight: 160),
      width: double.infinity,
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surface.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(icon, color: color, size: 22),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: isPositive
                            ? Colors.green.withValues(alpha: 0.1)
                            : Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isPositive
                                ? Icons.trending_up_rounded
                                : Icons.trending_down_rounded,
                            color: isPositive ? Colors.green : Colors.red,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            trend,
                            style: TextStyle(
                              color: isPositive ? Colors.green : Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء عنصر إحصائية أفقي
  Widget _buildHorizontalStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required ColorScheme colorScheme,
    required bool isDark,
    VoidCallback? onTap,
  }) {
    final bool isDisabled = _isTrialExpired();
    final Color effectiveColor = isDisabled ? Colors.grey : color;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: effectiveColor.withValues(alpha: isDisabled ? 0.05 : 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: effectiveColor.withValues(alpha: isDisabled ? 0.1 : 0.2),
          ),
        ),
        child: Stack(
          children: [
            Column(
              children: [
                Icon(icon, color: effectiveColor, size: 28),
                const SizedBox(height: 8),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: effectiveColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: isDisabled
                        ? Colors.grey.withValues(alpha: 0.7)
                        : colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            if (isDisabled)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Center(
                    child: Icon(Icons.lock, color: Colors.grey, size: 20),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // بناء صف الديون المنفصل
  Widget _buildDebtStatRow(ColorScheme colorScheme, bool isDark) {
    final bool isDisabled = _isTrialExpired();
    final Color effectiveColor = isDisabled ? Colors.grey : Colors.brown;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: effectiveColor.withValues(alpha: isDisabled ? 0.05 : 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: effectiveColor.withValues(alpha: isDisabled ? 0.1 : 0.2),
        ),
      ),
      child: Stack(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: effectiveColor.withValues(
                    alpha: isDisabled ? 0.1 : 0.2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.attach_money_rounded,
                  color: effectiveColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الديون',
                      style: TextStyle(
                        fontSize: 14,
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_totalDebt.toStringAsFixed(0)} د.ع',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: effectiveColor,
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () async {
                  // فحص إذا كان الحساب منتهي
                  if (_isTrialExpired()) {
                    _showAccountExpiredDialog();
                    return;
                  }

                  if (_debtSubscribers == 0) {
                    if (!mounted) return;
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('لا يوجد مشتركين عليهم ديون.'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                    return;
                  }
                  setState(() {
                    _currentFilter = {'totalDebt': '>0'};
                    _currentIndex = 1;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: effectiveColor.withValues(
                      alpha: isDisabled ? 0.1 : 0.2,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: effectiveColor,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          if (isDisabled)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(
                  child: Icon(Icons.lock, color: Colors.grey, size: 20),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // بناء بطاقة إحصائية عصرية
  Widget _buildModernStatCard({
    required IconData icon,
    required String label,
    required String value,
    required String subLabel,
    required Color color,
    required ColorScheme colorScheme,
    required bool isDark,
    VoidCallback? onTap,
  }) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      child: InkWell(
        borderRadius: BorderRadius.circular(18),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const Spacer(),
                  if (onTap != null)
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: colorScheme.onSurface.withValues(alpha: 0.5),
                      size: 16,
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                value,
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subLabel,
                style: TextStyle(
                  fontSize: 12,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // تم إزالة منطق Firebase القديم - الآن يتم التحكم من SimpleRootScreen

    debugPrint('[MAIN-HOME] build - عرض الشاشة الرئيسية');

    return ValueListenableBuilder<bool>(
      valueListenable: anyDeviceConnectedNotifier,
      builder: (context, anyConnected, child) {
        return Scaffold(
          body: _screens[_currentIndex],
          bottomNavigationBar: SafeArea(
            top: false,
            child: ValueListenableBuilder<bool>(
              valueListenable: anyDeviceConnectedNotifier,
              builder: (context, anyConnected, child) {
                final colorScheme = Theme.of(context).colorScheme;
                final isDark = Theme.of(context).brightness == Brightness.dark;

                return ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(18),
                  ),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 800),
                    curve: Curves.easeInOut,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: anyConnected
                            ? [
                                // متصل - تدرج أخضر حيوي
                                Colors.green.shade400.withValues(
                                  alpha: isDark ? 0.4 : 0.3,
                                ),
                                Colors.green.shade300.withValues(
                                  alpha: isDark ? 0.2 : 0.15,
                                ),
                                Colors.green.shade100.withValues(
                                  alpha: isDark ? 0.1 : 0.08,
                                ),
                                colorScheme.surface.withValues(
                                  alpha: isDark ? 0.95 : 0.98,
                                ),
                              ]
                            : [
                                // منقطع - تدرج أحمر/رمادي
                                Colors.red.shade400.withValues(
                                  alpha: isDark ? 0.35 : 0.25,
                                ),
                                Colors.orange.shade300.withValues(
                                  alpha: isDark ? 0.2 : 0.15,
                                ),
                                Colors.grey.shade300.withValues(
                                  alpha: isDark ? 0.15 : 0.1,
                                ),
                                colorScheme.surface.withValues(
                                  alpha: isDark ? 0.95 : 0.98,
                                ),
                              ],
                        stops: const [0.0, 0.3, 0.7, 1.0],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: anyConnected
                              ? Colors.green.withValues(alpha: 0.2)
                              : Colors.red.withValues(alpha: 0.15),
                          blurRadius: 8,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: BottomNavigationBar(
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                      currentIndex: _currentIndex,
                      onTap: (i) async {
                        // فحص إذا كان الحساب منتهي وليس الشاشة الرئيسية أو الإعدادات
                        if (_isTrialExpired() && i != 0 && i != 3) {
                          _showAccountExpiredDialog();
                          return;
                        }

                        // إذا كان زر الأجهزة (الفهرس 2)، أظهر رسالة "قريباً"
                        if (i == 2) {
                          _showComingSoonDialog();
                          return;
                        }

                        if (i == 0) {
                          await _loadStatsIfNeeded();
                        }
                        setState(() {
                          if (i != 1) _currentFilter = null;
                          _currentIndex = i;
                        });
                      },
                      selectedItemColor: Theme.of(context).colorScheme.primary,
                      unselectedItemColor: Theme.of(
                        context,
                      ).colorScheme.onSurfaceVariant,
                      iconSize: 28,
                      items: [
                        _buildNavBarItem(
                          Icons.home,
                          'الرئيسية',
                          0,
                          anyConnected,
                        ),
                        _buildNavBarItem(
                          Icons.people,
                          'المشتركين',
                          1,
                          anyConnected,
                        ),
                        _buildNavBarItem(
                          Icons.wifi,
                          'الأجهزة',
                          2,
                          anyConnected,
                        ),
                        _buildNavBarItem(
                          Icons.settings,
                          'الإعدادات',
                          3,
                          anyConnected,
                        ),
                      ],
                      type: BottomNavigationBarType.fixed,
                      showUnselectedLabels: true,
                      showSelectedLabels: true,
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// إظهار رسالة انتهاء الحساب للميزات المحظورة
  void _showAccountExpiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(Icons.lock_clock, color: Colors.orange, size: 28),
              const SizedBox(width: 12),
              const Text(
                'حساب منتهي الصلاحية',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            'هذه الميزة متاحة للحسابات المفعلة فقط.\nيرجى تفعيل حسابك للوصول إلى جميع الميزات.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              icon: const Icon(Icons.upgrade, size: 18),
              label: const Text('تفعيل الحساب'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () async {
                Navigator.of(context).pop();
                // الانتقال مباشرة إلى شاشة الحساب للتفعيل
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SupabaseAccountScreen(
                      profileImageFile: _profileImageFile,
                      onImageChanged: (File? newImage) {
                        setState(() {
                          _profileImageFile = newImage;
                        });
                      },
                    ),
                  ),
                );

                // تحديث اسم المستخدم عند العودة من شاشة الحساب
                if (mounted) {
                  await _loadUserName();
                  setState(() {}); // إعادة بناء الواجهة لإظهار الاسم المحدث
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// إظهار رسالة "قريباً" لشاشة الأجهزة
  void _showComingSoonDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.wifi,
                color: Theme.of(context).colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'شاشة الأجهزة',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.construction, size: 64, color: Colors.orange[400]),
              const SizedBox(height: 16),
              const Text(
                'قريباً...',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'نعمل على تطوير شاشة إدارة الأجهزة\nوستكون متاحة قريباً',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text(
                'موافق',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }

  // بناء عنصر إحصائية
  Widget _buildStatItem(String label, int value, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 8),
        Text(
          '$value',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  BottomNavigationBarItem _buildNavBarItem(
    IconData icon,
    String label,
    int index,
    bool isConnected,
  ) {
    final bool isActive = _currentIndex == index;
    final colorScheme = Theme.of(context).colorScheme;
    return BottomNavigationBarItem(
      icon: AnimatedContainer(
        duration: const Duration(milliseconds: 180),
        curve: Curves.easeOut,
        margin: EdgeInsets.only(bottom: isActive ? 2 : 0),
        child: AnimatedScale(
          scale: isActive ? 1.18 : 1.0,
          duration: const Duration(milliseconds: 180),
          curve: Curves.easeOut,
          child: Container(
            decoration: isActive
                ? BoxDecoration(
                    color: colorScheme.surface,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withOpacity(0.10),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  )
                : null,
            padding: isActive ? const EdgeInsets.all(3) : EdgeInsets.zero,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 600),
              curve: Curves.easeInOut,
              child: Icon(
                icon,
                size: isActive ? 35 : 28,
                color: isActive
                    ? (isConnected ? colorScheme.primary : Colors.orange)
                    : (isConnected
                          ? colorScheme.onSurfaceVariant
                          : colorScheme.onSurfaceVariant.withValues(
                              alpha: 0.6,
                            )),
              ),
            ),
          ),
        ),
      ),
      label: isActive ? '' : label,
    );
  }

  // جلب تفاصيل الحساب من Supabase
  Future<Map<String, dynamic>> _getAccountDetails() async {
    try {
      // استخدام AccountService للحصول على بيانات الحساب من Supabase
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        return {
          'isTrial': true,
          'daysLeft': 0,
          'expiryDate': null,
          'activePackage': '',
        };
      }

      final accountData = await AccountService.getAccountDataV2(user.id);
      if (accountData == null) {
        return {
          'isTrial': true,
          'daysLeft': 0,
          'expiryDate': null,
          'activePackage': '',
        };
      }

      final accountStatus = accountData['account_status'] as String?;
      final isTrial = accountStatus == 'trial';

      // جلب الأيام المتبقية من السيرفر بدلاً من الحساب المحلي
      int daysLeft = await AccountService.getDaysLeft(user.id);

      DateTime? expiryDate;
      if (isTrial && accountData['trial_end'] != null) {
        expiryDate = DateTime.parse(accountData['trial_end']);
      } else if (accountStatus == 'active' &&
          accountData['subscription_end'] != null) {
        expiryDate = DateTime.parse(accountData['subscription_end']);
      }

      return {
        'isTrial': isTrial,
        'daysLeft': daysLeft,
        'expiryDate': expiryDate,
        'activePackage': accountData['subscription_type'] ?? '',
      };
    } catch (e) {
      debugPrint('خطأ في جلب تفاصيل الحساب: $e');
      return {
        'isTrial': true,
        'daysLeft': 0,
        'expiryDate': null,
        'activePackage': '',
      };
    }
  }
}

// إشعار مخصص لتحديث حالة اتصال السيرفرات
class ServerConnectionNotifier extends ValueNotifier<bool> {
  ServerConnectionNotifier() : super(false);

  void updateConnectionStatus(bool isConnected) {
    value = isConnected;
  }
}

// Modern horizontal stats bar widget
class _ModernStatsBar extends StatelessWidget {
  final int total;
  final int active;
  final int expired;
  final int debt;
  final VoidCallback? onTotalTap;
  final VoidCallback? onActiveTap;
  final VoidCallback? onExpiredTap;
  final VoidCallback? onDebtTap;

  const _ModernStatsBar({
    Key? key,
    required this.total,
    required this.active,
    required this.expired,
    required this.debt,
    this.onTotalTap,
    this.onActiveTap,
    this.onExpiredTap,
    this.onDebtTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    final stats = [
      _BarStat(
        label: 'الكل',
        value: total,
        percentage: 1.0,
        color: colorScheme.primary,
        icon: Icons.people,
        onTap: onTotalTap,
      ),
      _BarStat(
        label: 'نشط',
        value: active,
        percentage: total > 0 ? active / total : 0,
        color: Colors.green,
        icon: Icons.check_circle,
        onTap: onActiveTap,
      ),
      _BarStat(
        label: 'منتهي',
        value: expired,
        percentage: total > 0 ? expired / total : 0,
        color: Colors.orange,
        icon: Icons.schedule,
        onTap: onExpiredTap,
      ),
      _BarStat(
        label: 'مديون',
        value: debt,
        percentage: total > 0 ? debt / total : 0,
        color: Colors.red,
        icon: Icons.account_balance_wallet,
        onTap: onDebtTap,
      ),
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surface.withValues(alpha: 0.8)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: stats.map((stat) {
          return Expanded(
            child: GestureDetector(
              onTap: stat.onTap,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 8,
                ),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: stat.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Icon(stat.icon, color: stat.color, size: 24),
                    const SizedBox(height: 8),
                    Text(
                      '${stat.value}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: stat.color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      stat.label,
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: stat.color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: stat.percentage,
                        child: Container(
                          decoration: BoxDecoration(
                            color: stat.color,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

class _BarStat {
  final String label;
  final int value;
  final double percentage;
  final Color color;
  final IconData icon;
  final VoidCallback? onTap;

  _BarStat({
    required this.label,
    required this.value,
    required this.percentage,
    required this.color,
    required this.icon,
    this.onTap,
  });
}

// إشعار مخصص لتحديث حالة اتصال السيرفرات
class ServerStatusChangedNotification extends Notification {}

// إشعار مخصص لتحديث حالة اتصال اللوحات
class BoardStatusChangedNotification extends Notification {}
