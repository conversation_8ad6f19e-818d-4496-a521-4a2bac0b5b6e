-- ===================================================================
-- نظام إدارة تحديثات التطبيق
-- ===================================================================

-- 1. جدول معلومات التحديثات
CREATE TABLE IF NOT EXISTS app_updates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    build_number INTEGER NOT NULL,
    platform VARCHAR(20) NOT NULL CHECK (platform IN ('android', 'ios', 'web')),
    is_active BOOLEAN DEFAULT true,
    is_forced BOOLEAN DEFAULT false,
    min_compatible_version VARCHAR(20),
    database_migration_required BOOLEAN DEFAULT false,
    release_notes TEXT,
    download_url TEXT,
    features JSONB DEFAULT '[]'::jsonb,
    bug_fixes JSONB DEFAULT '[]'::jsonb,
    security_updates JSONB DEFAULT '[]'::jsonb,
    file_size_mb DECIMAL(10,2),
    checksum VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES auth.users(id),
    
    -- فهارس للأداء
    UNIQUE(version, build_number, platform)
);

-- 2. جدول سجل تحديثات المستخدمين
CREATE TABLE IF NOT EXISTS user_update_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    update_id UUID NOT NULL REFERENCES app_updates(id) ON DELETE CASCADE,
    from_version VARCHAR(20) NOT NULL,
    to_version VARCHAR(20) NOT NULL,
    update_type VARCHAR(20) NOT NULL CHECK (update_type IN ('automatic', 'manual', 'forced')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('started', 'completed', 'failed', 'skipped')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    device_info JSONB,
    backup_created BOOLEAN DEFAULT false,
    data_migrated BOOLEAN DEFAULT false
);

-- 3. جدول إعدادات التحديث للمستخدمين
CREATE TABLE IF NOT EXISTS user_update_preferences (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    auto_check_enabled BOOLEAN DEFAULT true,
    auto_download_enabled BOOLEAN DEFAULT false,
    wifi_only_download BOOLEAN DEFAULT true,
    notification_enabled BOOLEAN DEFAULT true,
    skipped_versions JSONB DEFAULT '[]'::jsonb,
    last_check_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول إحصائيات التحديثات
CREATE TABLE IF NOT EXISTS update_statistics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    update_id UUID NOT NULL REFERENCES app_updates(id) ON DELETE CASCADE,
    platform VARCHAR(20) NOT NULL,
    total_users INTEGER DEFAULT 0,
    successful_updates INTEGER DEFAULT 0,
    failed_updates INTEGER DEFAULT 0,
    skipped_updates INTEGER DEFAULT 0,
    average_download_time_seconds INTEGER,
    average_install_time_seconds INTEGER,
    date DATE DEFAULT CURRENT_DATE,

    UNIQUE(update_id, platform, date)
);

-- ===================================================================
-- إنشاء الفهارس للأداء
-- ===================================================================

-- فهارس جدول user_update_history
CREATE INDEX IF NOT EXISTS idx_user_update_history_user_id ON user_update_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_update_history_update_id ON user_update_history(update_id);
CREATE INDEX IF NOT EXISTS idx_user_update_history_status ON user_update_history(status);

-- فهارس جدول update_statistics
CREATE INDEX IF NOT EXISTS idx_update_statistics_update_id ON update_statistics(update_id);
CREATE INDEX IF NOT EXISTS idx_update_statistics_date ON update_statistics(date);

-- فهارس إضافية لجدول app_updates
CREATE INDEX IF NOT EXISTS idx_app_updates_platform_active ON app_updates(platform, is_active);
CREATE INDEX IF NOT EXISTS idx_app_updates_build_number ON app_updates(build_number);
CREATE INDEX IF NOT EXISTS idx_app_updates_published_at ON app_updates(published_at);

-- ===================================================================
-- الدوال المساعدة
-- ===================================================================

-- 5. دالة الحصول على أحدث تحديث متاح
CREATE OR REPLACE FUNCTION get_latest_update(
    user_platform TEXT,
    current_build_number INTEGER DEFAULT 0
)
RETURNS TABLE (
    version TEXT,
    build_number INTEGER,
    is_forced BOOLEAN,
    is_compatible BOOLEAN,
    release_notes TEXT,
    download_url TEXT,
    features JSONB,
    bug_fixes JSONB,
    security_updates JSONB,
    database_migration_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        au.version::TEXT,
        au.build_number,
        au.is_forced,
        CASE 
            WHEN au.min_compatible_version IS NULL THEN true
            ELSE au.build_number >= current_build_number
        END as is_compatible,
        au.release_notes,
        au.download_url,
        au.features,
        au.bug_fixes,
        au.security_updates,
        au.database_migration_required
    FROM app_updates au
    WHERE au.platform = user_platform
        AND au.is_active = true
        AND au.build_number > current_build_number
        AND (au.published_at IS NULL OR au.published_at <= NOW())
    ORDER BY au.build_number DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. دالة تسجيل محاولة التحديث
CREATE OR REPLACE FUNCTION log_update_attempt(
    p_user_id UUID,
    p_update_id UUID,
    p_from_version TEXT,
    p_to_version TEXT,
    p_update_type TEXT,
    p_device_info JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    history_id UUID;
BEGIN
    INSERT INTO user_update_history (
        user_id,
        update_id,
        from_version,
        to_version,
        update_type,
        device_info,
        status
    ) VALUES (
        p_user_id,
        p_update_id,
        p_from_version,
        p_to_version,
        p_update_type,
        p_device_info,
        'started'
    ) RETURNING id INTO history_id;
    
    RETURN history_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. دالة تحديث حالة التحديث
CREATE OR REPLACE FUNCTION update_update_status(
    p_history_id UUID,
    p_status TEXT,
    p_error_message TEXT DEFAULT NULL,
    p_backup_created BOOLEAN DEFAULT NULL,
    p_data_migrated BOOLEAN DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_update_history 
    SET 
        status = p_status,
        completed_at = CASE WHEN p_status IN ('completed', 'failed', 'skipped') THEN NOW() ELSE completed_at END,
        error_message = COALESCE(p_error_message, error_message),
        backup_created = COALESCE(p_backup_created, backup_created),
        data_migrated = COALESCE(p_data_migrated, data_migrated),
        updated_at = NOW()
    WHERE id = p_history_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة تحديث إحصائيات التحديث
CREATE OR REPLACE FUNCTION update_update_statistics(
    p_update_id UUID,
    p_platform TEXT,
    p_status TEXT
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO update_statistics (update_id, platform, total_users)
    VALUES (p_update_id, p_platform, 1)
    ON CONFLICT (update_id, platform, date)
    DO UPDATE SET
        total_users = update_statistics.total_users + 1,
        successful_updates = CASE WHEN p_status = 'completed' THEN update_statistics.successful_updates + 1 ELSE update_statistics.successful_updates END,
        failed_updates = CASE WHEN p_status = 'failed' THEN update_statistics.failed_updates + 1 ELSE update_statistics.failed_updates END,
        skipped_updates = CASE WHEN p_status = 'skipped' THEN update_statistics.skipped_updates + 1 ELSE update_statistics.skipped_updates END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. دالة الحصول على تفضيلات التحديث للمستخدم
CREATE OR REPLACE FUNCTION get_user_update_preferences(p_user_id UUID)
RETURNS TABLE (
    auto_check_enabled BOOLEAN,
    auto_download_enabled BOOLEAN,
    wifi_only_download BOOLEAN,
    notification_enabled BOOLEAN,
    skipped_versions JSONB,
    last_check_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- إنشاء تفضيلات افتراضية إذا لم تكن موجودة
    INSERT INTO user_update_preferences (user_id)
    VALUES (p_user_id)
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN QUERY
    SELECT 
        uup.auto_check_enabled,
        uup.auto_download_enabled,
        uup.wifi_only_download,
        uup.notification_enabled,
        uup.skipped_versions,
        uup.last_check_at
    FROM user_update_preferences uup
    WHERE uup.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. دالة تحديث تفضيلات المستخدم
CREATE OR REPLACE FUNCTION update_user_update_preferences(
    p_user_id UUID,
    p_auto_check_enabled BOOLEAN DEFAULT NULL,
    p_auto_download_enabled BOOLEAN DEFAULT NULL,
    p_wifi_only_download BOOLEAN DEFAULT NULL,
    p_notification_enabled BOOLEAN DEFAULT NULL,
    p_skipped_versions JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO user_update_preferences (user_id)
    VALUES (p_user_id)
    ON CONFLICT (user_id) 
    DO UPDATE SET
        auto_check_enabled = COALESCE(p_auto_check_enabled, user_update_preferences.auto_check_enabled),
        auto_download_enabled = COALESCE(p_auto_download_enabled, user_update_preferences.auto_download_enabled),
        wifi_only_download = COALESCE(p_wifi_only_download, user_update_preferences.wifi_only_download),
        notification_enabled = COALESCE(p_notification_enabled, user_update_preferences.notification_enabled),
        skipped_versions = COALESCE(p_skipped_versions, user_update_preferences.skipped_versions),
        updated_at = NOW();
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. دالة للتحقق من صلاحيات الإدارة
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_accounts
        WHERE user_id = auth.uid()
        AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===================================================================
-- السياسات الأمنية (RLS)
-- ===================================================================

-- تفعيل RLS على الجداول
ALTER TABLE app_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_update_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_update_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE update_statistics ENABLE ROW LEVEL SECURITY;

-- سياسة الوصول لسجل التحديثات
CREATE POLICY "Users can view their own update history" ON user_update_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own update history" ON user_update_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own update history" ON user_update_history
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسة الوصول لتفضيلات التحديث
CREATE POLICY "Users can manage their own update preferences" ON user_update_preferences
    FOR ALL USING (auth.uid() = user_id);

-- ===================================================================
-- سياسات جدول التحديثات (app_updates)
-- ===================================================================

-- المستخدمون العاديون يمكنهم قراءة التحديثات النشطة والمنشورة فقط
CREATE POLICY "Users can view active published updates" ON app_updates
    FOR SELECT USING (
        is_active = true
        AND (published_at IS NULL OR published_at <= NOW())
    );

-- المدراء يمكنهم إدارة جميع التحديثات
CREATE POLICY "Admins can manage all updates" ON app_updates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_accounts
            WHERE user_id = auth.uid()
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- تطبيق الإدارة يمكنه إدارة جميع التحديثات (عبر service role)
-- هذا يتم تطبيقه عبر service_role key في تطبيق الإدارة

-- ===================================================================
-- سياسات جدول إحصائيات التحديثات
-- ===================================================================

-- المدراء فقط يمكنهم رؤية الإحصائيات
CREATE POLICY "Admins can view update statistics" ON update_statistics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_accounts
            WHERE user_id = auth.uid()
            AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
        )
    );

-- تطبيق الإدارة يمكنه إدارة الإحصائيات (عبر service role)
CREATE POLICY "Service role can manage statistics" ON update_statistics
    FOR ALL USING (auth.role() = 'service_role');

-- ===================================================================
-- منح الصلاحيات
-- ===================================================================

-- منح صلاحيات للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON app_updates TO authenticated;
GRANT ALL ON user_update_history TO authenticated;
GRANT ALL ON user_update_preferences TO authenticated;
GRANT SELECT ON update_statistics TO authenticated;

-- منح صلاحيات لـ service role (تطبيق الإدارة)
GRANT ALL ON app_updates TO service_role;
GRANT ALL ON user_update_history TO service_role;
GRANT ALL ON user_update_preferences TO service_role;
GRANT ALL ON update_statistics TO service_role;

-- منح صلاحيات على التسلسلات
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- ===================================================================
-- البيانات الأولية
-- ===================================================================

-- إدراج تحديث تجريبي
INSERT INTO app_updates (
    version,
    build_number,
    platform,
    is_active,
    is_forced,
    min_compatible_version,
    database_migration_required,
    release_notes,
    features,
    bug_fixes,
    security_updates,
    published_at
) VALUES 
(
    '1.1.0',
    2,
    'android',
    true,
    false,
    '1.0.0',
    false,
    'تحديث جديد مع تحسينات في الأداء وإصلاحات للأخطاء',
    '["تحسين واجهة المستخدم", "إضافة ميزة البحث المتقدم", "تحسين سرعة التطبيق"]'::jsonb,
    '["إصلاح مشكلة تسجيل الدخول", "إصلاح مشكلة المزامنة", "إصلاح مشاكل الأداء"]'::jsonb,
    '["تحسين أمان البيانات", "إصلاح ثغرات أمنية"]'::jsonb,
    NOW()
) ON CONFLICT (version, build_number, platform) DO NOTHING;
