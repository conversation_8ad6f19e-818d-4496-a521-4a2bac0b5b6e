import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../managers/unified_account_status_manager.dart';
import '../managers/unified_restriction_manager.dart';
import '../managers/internet_status_manager.dart';
import 'unified_data_flow_service.dart';
import 'unified_monitoring_service.dart';

/// خدمة التهيئة الموحدة
/// تدير تهيئة جميع أجزاء النظام بالترتيب الصحيح
class UnifiedInitializationService {
  static const String _tag = '[UNIFIED_INIT]';

  static bool _isInitialized = false;
  static bool _isInitializing = false;

  /// فحص إذا كان النظام مهيأ
  static bool get isInitialized => _isInitialized;
  static bool get isInitializing => _isInitializing;

  /// تهيئة النظام الموحد
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('$_tag النظام مهيأ بالفعل');
      return;
    }

    if (_isInitializing) {
      debugPrint('$_tag التهيئة جارية...');
      return;
    }

    _isInitializing = true;

    try {
      debugPrint('$_tag بدء تهيئة النظام الموحد...');

      // 1. تهيئة مدير حالة الإنترنت
      await _initializeInternetManager();

      // 2. التحقق من الاتصال بالإنترنت
      await _checkInternetConnection();

      // 3. تهيئة Supabase (إذا لم يكن مهيأ)
      await _initializeSupabase();

      // 4. تهيئة خدمة تدفق البيانات
      await _initializeDataFlow();

      // 5. تهيئة مدير حالة الحساب
      await _initializeAccountManager();

      // 6. تطبيق القيود الأولية
      await _applyInitialRestrictions();

      // 7. بدء المراقبة
      await _startMonitoring();

      _isInitialized = true;
      debugPrint('$_tag ✅ تم تهيئة النظام الموحد بنجاح');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تهيئة النظام: $e');
      _isInitialized = false;
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  /// تهيئة مدير حالة الإنترنت
  static Future<void> _initializeInternetManager() async {
    try {
      debugPrint('$_tag تهيئة مدير حالة الإنترنت...');

      if (!InternetStatusManager.isInitialized) {
        await InternetStatusManager.initialize();
      }

      debugPrint('$_tag ✅ تم تهيئة مدير حالة الإنترنت');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تهيئة مدير الإنترنت: $e');
      rethrow;
    }
  }

  /// فحص الاتصال بالإنترنت
  static Future<void> _checkInternetConnection() async {
    try {
      debugPrint('$_tag فحص الاتصال بالإنترنت...');

      // انتظار حتى يتم تحديد حالة الإنترنت
      int attempts = 0;
      const maxAttempts = 10;

      while (!InternetStatusManager.isInitialized && attempts < maxAttempts) {
        await Future.delayed(const Duration(milliseconds: 500));
        attempts++;
      }

      if (InternetStatusManager.isConnected) {
        debugPrint('$_tag ✅ الاتصال بالإنترنت متاح');
      } else {
        debugPrint(
          '$_tag ⚠️ لا يوجد اتصال بالإنترنت - سيتم العمل في الوضع المحلي',
        );
      }
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في فحص الإنترنت: $e');
      // لا نرمي الخطأ هنا لأن عدم وجود إنترنت ليس خطأ قاتل
    }
  }

  /// تهيئة Supabase
  static Future<void> _initializeSupabase() async {
    try {
      debugPrint('$_tag فحص تهيئة Supabase...');

      // فحص إذا كان Supabase مهيأ بالفعل
      final client = Supabase.instance.client;
      if (client.auth.currentUser != null) {
        debugPrint('$_tag ✅ Supabase مهيأ ومتصل');
        return;
      }

      // إذا لم يكن متصل، فحص الجلسة المحفوظة
      final session = client.auth.currentSession;
      if (session != null) {
        debugPrint('$_tag ✅ تم استرداد جلسة Supabase المحفوظة');
        return;
      }

      debugPrint('$_tag ⚠️ لا توجد جلسة Supabase نشطة');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في فحص Supabase: $e');
      // لا نرمي الخطأ هنا لأن عدم وجود جلسة ليس خطأ قاتل
    }
  }

  /// تهيئة خدمة تدفق البيانات
  static Future<void> _initializeDataFlow() async {
    try {
      debugPrint('$_tag تهيئة خدمة تدفق البيانات...');

      await UnifiedDataFlowService.initialize();

      debugPrint('$_tag ✅ تم تهيئة خدمة تدفق البيانات');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تهيئة خدمة تدفق البيانات: $e');
      rethrow;
    }
  }

  /// تهيئة مدير حالة الحساب
  static Future<void> _initializeAccountManager() async {
    try {
      debugPrint('$_tag تهيئة مدير حالة الحساب...');

      await UnifiedAccountStatusManager.initialize();

      debugPrint('$_tag ✅ تم تهيئة مدير حالة الحساب');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تهيئة مدير حالة الحساب: $e');
      rethrow;
    }
  }

  /// تطبيق القيود الأولية
  static Future<void> _applyInitialRestrictions() async {
    try {
      debugPrint('$_tag تطبيق القيود الأولية...');

      await UnifiedRestrictionManager.applySystemRestrictions();

      debugPrint('$_tag ✅ تم تطبيق القيود الأولية');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تطبيق القيود: $e');
      // لا نرمي الخطأ هنا لأن فشل تطبيق القيود ليس خطأ قاتل
    }
  }

  /// بدء المراقبة
  static Future<void> _startMonitoring() async {
    try {
      debugPrint('$_tag بدء مراقبة النظام...');

      await UnifiedMonitoringService.startMonitoring();

      debugPrint('$_tag ✅ تم بدء المراقبة');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في بدء المراقبة: $e');
      // لا نرمي الخطأ هنا لأن فشل المراقبة ليس خطأ قاتل
    }
  }

  /// تهيئة المستخدم
  static Future<void> initializeUser(String userId) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('$_tag تهيئة المستخدم: $userId');

      // تحديث معرف المستخدم في مدير حالة الحساب
      await UnifiedAccountStatusManager.updateUserId(userId);

      // تطبيق القيود المناسبة للمستخدم
      await UnifiedRestrictionManager.applySystemRestrictions();

      debugPrint('$_tag ✅ تم تهيئة المستخدم بنجاح');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في تهيئة المستخدم: $e');
      rethrow;
    }
  }

  /// إعادة تهيئة النظام
  static Future<void> reinitialize() async {
    debugPrint('$_tag إعادة تهيئة النظام...');

    // إنهاء النظام الحالي
    await dispose();

    // إعادة التهيئة
    await initialize();
  }

  /// إنهاء النظام
  static Future<void> dispose() async {
    debugPrint('$_tag إنهاء النظام...');

    try {
      // إنهاء جميع المدراء والخدمات
      UnifiedMonitoringService.dispose();
      UnifiedAccountStatusManager.dispose();
      UnifiedDataFlowService.dispose();

      _isInitialized = false;
      _isInitializing = false;

      debugPrint('$_tag ✅ تم إنهاء النظام');
    } catch (e) {
      debugPrint('$_tag ❌ خطأ في إنهاء النظام: $e');
    }
  }

  /// فحص صحة النظام
  static Map<String, dynamic> performHealthCheck() {
    final healthCheck = {
      'timestamp': DateTime.now().toIso8601String(),
      'isInitialized': _isInitialized,
      'isInitializing': _isInitializing,
      'components': <String, dynamic>{},
    };

    try {
      // فحص مدير الإنترنت
      final components = healthCheck['components'] as Map<String, dynamic>;
      components['internetManager'] = {
        'isInitialized': InternetStatusManager.isInitialized,
        'isConnected': InternetStatusManager.isConnected,
      };

      // فحص خدمة تدفق البيانات
      components['dataFlowService'] = UnifiedDataFlowService.getSystemStats();

      // فحص مدير حالة الحساب
      components['accountStatusManager'] =
          UnifiedAccountStatusManager.getManagerStats();

      // فحص مدير القيود
      components['restrictionManager'] =
          UnifiedRestrictionManager.getManagerStats();

      // تحديد الحالة العامة
      final allComponentsHealthy = components.values.every(
        (component) =>
            component is Map &&
            (component['isInitialized'] == true ||
                component['hasStatus'] == true),
      );

      healthCheck['overallHealth'] = allComponentsHealthy
          ? 'healthy'
          : 'unhealthy';
    } catch (e) {
      healthCheck['error'] = e.toString();
      healthCheck['overallHealth'] = 'error';
    }

    return healthCheck;
  }

  /// طباعة تقرير حالة النظام
  static void printSystemStatus() {
    final healthCheck = performHealthCheck();

    debugPrint('$_tag === تقرير حالة النظام ===');
    debugPrint('$_tag الحالة العامة: ${healthCheck['overallHealth']}');
    debugPrint('$_tag مهيأ: ${healthCheck['isInitialized']}');
    debugPrint('$_tag قيد التهيئة: ${healthCheck['isInitializing']}');

    final components = healthCheck['components'] as Map<String, dynamic>;
    for (final entry in components.entries) {
      debugPrint('$_tag ${entry.key}: ${entry.value}');
    }

    debugPrint('$_tag === نهاية التقرير ===');
  }
}
