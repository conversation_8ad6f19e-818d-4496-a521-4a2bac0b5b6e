import 'package:flutter/material.dart';
import 'dart:async';
import '../core/managers/internet_status_manager.dart';

/// شاشة فحص الإنترنت عند بداية التطبيق
class InternetCheckScreen extends StatefulWidget {
  final VoidCallback onInternetConnected;
  
  const InternetCheckScreen({
    Key? key,
    required this.onInternetConnected,
  }) : super(key: key);

  @override
  State<InternetCheckScreen> createState() => _InternetCheckScreenState();
}

class _InternetCheckScreenState extends State<InternetCheckScreen>
    with TickerProviderStateMixin {
  
  bool _isChecking = true;
  bool _hasInternet = false;
  String _statusMessage = 'فحص الاتصال بالإنترنت...';
  
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  
  StreamSubscription<bool>? _internetSubscription;
  Timer? _recheckTimer;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startInternetCheck();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _internetSubscription?.cancel();
    _recheckTimer?.cancel();
    super.dispose();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
  }

  Future<void> _startInternetCheck() async {
    setState(() {
      _isChecking = true;
      _statusMessage = 'تهيئة فحص الإنترنت...';
    });

    try {
      // تهيئة مدير حالة الإنترنت
      await InternetStatusManager.initialize();
      
      // الاستماع لتغييرات حالة الإنترنت
      _internetSubscription = InternetStatusManager.statusStream.listen(
        (isConnected) {
          _handleInternetStatusChange(isConnected);
        },
      );

      // فحص الحالة الأولية
      final isConnected = InternetStatusManager.isConnected;
      _handleInternetStatusChange(isConnected);
      
    } catch (e) {
      debugPrint('❌ [INTERNET_CHECK] خطأ في فحص الإنترنت: $e');
      setState(() {
        _isChecking = false;
        _hasInternet = false;
        _statusMessage = 'خطأ في فحص الاتصال';
      });
    }
  }

  void _handleInternetStatusChange(bool isConnected) {
    if (!mounted) return;

    setState(() {
      _hasInternet = isConnected;
      _isChecking = false;
      
      if (isConnected) {
        _statusMessage = 'تم الاتصال بالإنترنت بنجاح!';
        _pulseController.stop();
        _rotationController.stop();
        
        // الانتقال للشاشة التالية بعد ثانية واحدة
        Timer(const Duration(seconds: 1), () {
          if (mounted) {
            widget.onInternetConnected();
          }
        });
      } else {
        _statusMessage = 'لا يوجد اتصال بالإنترنت';
        _pulseController.repeat(reverse: true);
        _rotationController.repeat();
      }
    });
  }

  Future<void> _retryConnection() async {
    setState(() {
      _isChecking = true;
      _statusMessage = 'إعادة فحص الاتصال...';
    });

    _pulseController.repeat(reverse: true);
    _rotationController.repeat();

    // فحص فوري للاتصال
    final isConnected = await InternetStatusManager.checkConnection();
    _handleInternetStatusChange(isConnected);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار التطبيق أو أيقونة
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.wifi_find,
                  size: 50,
                  color: Colors.blue[600],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // عنوان التطبيق
              Text(
                'iTower',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
              
              const SizedBox(height: 48),
              
              // أيقونة حالة الإنترنت
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _isChecking ? _pulseAnimation.value : 1.0,
                    child: AnimatedBuilder(
                      animation: _rotationAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _isChecking ? _rotationAnimation.value * 2 * 3.14159 : 0,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: _hasInternet ? Colors.green[100] : 
                                     _isChecking ? Colors.blue[100] : Colors.red[100],
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _hasInternet ? Icons.wifi : 
                              _isChecking ? Icons.wifi_find : Icons.wifi_off,
                              size: 40,
                              color: _hasInternet ? Colors.green[700] : 
                                     _isChecking ? Colors.blue[700] : Colors.red[700],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 24),
              
              // رسالة الحالة
              Text(
                _statusMessage,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: _hasInternet ? Colors.green[700] : 
                         _isChecking ? Colors.blue[700] : Colors.red[700],
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // مؤشر التحميل أو زر إعادة المحاولة
              if (_isChecking)
                Column(
                  children: [
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'يرجى الانتظار...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                )
              else if (!_hasInternet)
                Column(
                  children: [
                    // زر إعادة المحاولة
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _retryConnection,
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // نصائح للمستخدم
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline, color: Colors.orange[700], size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'نصائح للاتصال:',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.orange[700],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '• تأكد من تشغيل الواي فاي أو البيانات\n'
                            '• تحقق من قوة الإشارة\n'
                            '• جرب الاتصال بشبكة أخرى',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.orange[800],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              else
                // رسالة النجاح
                Column(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 40,
                      color: Colors.green[600],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'جاري تحميل التطبيق...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
